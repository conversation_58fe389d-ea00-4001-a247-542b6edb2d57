// ignore_for_file: must_be_immutable, invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member

/*
 * Created Date: 2/03/2024 09:37:53
 * Author: gapo
 * -----
 * Last Modified: Tuesday, 5th March 2024 17:17:57
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';

import '../../app/app.dart';
import '../../domain/entity/enums/home/<USER>';
import '../../domain/entity/enums/home/<USER>';
import '../../domain/entity/ticket/ticket_list.entity.dart';
import '../../route/route.dart';
import '../../widgets/add_ticket_btn.dart';
import '../home/<USER>';
import '../home/<USER>';

Route? currentRoute;

TicketHomeMenuEnums routeCurrentHomeMenu =
    TicketHomeMenuEnums.values.firstWhereOrNull((e) => e.isSelected) ??
        TicketHomeMenuEnums.sendToMe;
int defaultTabIndex = 0;

// BuildContext? ticketBuildContext;

final class GPTicketMainPage extends StatelessWidget {
  const GPTicketMainPage({super.key});

  void onPopInvoked(BuildContext context, bool didPop) {
    if (Platform.isIOS) {
      SystemNavigator.pop(animated: true);
      return;
    }

    if (currentRoute?.isFirst == true) {
      SystemNavigator.pop(animated: true);
    } else {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        onPopInvoked(context, didPop);
      },
      child: BlocProvider(
        // lazy: false,
        create: (context) =>
            GetIt.I<AppBloc>()..add(const AppGetWorkFlowEvent()),
        child: TicketMainPage(
          homeMenu: routeCurrentHomeMenu,
          defaultTabIndex: defaultTabIndex,
        ),
      ),
    );
  }
}

final class TicketMainPage extends StatefulWidget {
  const TicketMainPage({
    required this.homeMenu,
    this.defaultTabIndex = 0,
    super.key,
  });

  final TicketHomeMenuEnums homeMenu;

  /// update logic nếu có yêu cầu thay đổi defaultTab
  final int defaultTabIndex;

  @override
  State<TicketMainPage> createState() => _TicketMainPageState();
}

class _TicketMainPageState extends State<TicketMainPage>
    with TickerProviderStateMixin {
  late final ValueNotifier<TicketHomeMenuEnums> rxHomeMenu =
      ValueNotifier(widget.homeMenu);

  late TabController tabController;

  Future openHomeMenuPick(BuildContext context) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute<TicketHomeMenuEnums>(
        builder: (BuildContext context) => HomeMenuWidget(),
      ),
    );

    // const TicketCategoryPickerRouteData().push(context);
    if (result != null) {
      routeCurrentHomeMenu = result;
      rxHomeMenu.value = result;
      initTabController(tabIndex: 0);

      rxHomeMenu.notifyListeners();
    }
  }

  void _onTicketCreated(BuildContext context, TicketEntity? entity) {
    routeCurrentHomeMenu = TicketHomeMenuEnums.createdByMe;

    final createByMeAllTab = routeCurrentHomeMenu.homeTabEntity.tabs
        .firstWhereOrNull((e) => e == TicketHomeTabs.all);
    // final int defaultTabIndex = createByMeAllTab != null
    //     ? routeCurrentHomeMenu.homeTabEntity.tabs.indexOf(createByMeAllTab)
    //     : 0;

    _onTicketReloadData(
        context: context,
        homeMenu: routeCurrentHomeMenu,
        homeTab: createByMeAllTab!);

    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_create_create_success.tr,
      type: SnackbarType.success,
    );
  }

  void _onTicketReloadData({
    required BuildContext context,
    TicketHomeMenuEnums? homeMenu,
    required TicketHomeTabs homeTab,
  }) {
    routeCurrentHomeMenu = homeMenu ?? rxHomeMenu.value;

    // Case action khi ở menu chưa có người xử lý
    if (routeCurrentHomeMenu == TicketHomeMenuEnums.noHandlers) {
      routeCurrentHomeMenu = TicketHomeMenuEnums.sendToMe;
    }

    final newHomeTab = routeCurrentHomeMenu.homeTabEntity.tabs
        .firstWhereOrNull((e) => e == homeTab);
    final int tabIndex = newHomeTab != null
        ? routeCurrentHomeMenu.homeTabEntity.tabs.indexOf(newHomeTab)
        : 0;
    defaultTabIndex = tabIndex;

    Get.offAllNamed(TicketRoutes.ticketMain);
  }

  void initTabController({int? tabIndex}) {
    final tabs = rxHomeMenu.value.homeTabEntity.tabs;
    tabController = TabController(
        length: tabs.length,
        vsync: this,
        initialIndex: tabIndex ?? widget.defaultTabIndex);
  }

  @override
  void initState() {
    initTabController();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant TicketMainPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    WidgetsBinding.instance.addPostFrameCallback(
        (_) => tabController.animateTo(widget.defaultTabIndex));
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: rxHomeMenu,
      builder: (context, value, child) {
        final tabs = value.homeTabEntity.tabs;
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            leading: GPBackButton(action: () {
              SystemNavigator.pop(animated: true);
            }),
            title: Text(
              value.displayName,
              style: textStyle(GPTypography.headingMedium),
            ),
            actions: [
              TextButton(
                style: TextButton.styleFrom(
                  foregroundColor: GPColor.contentSecondary,
                ),
                onPressed: () => openHomeMenuPick(context),
                child: const SvgWidget(
                  Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_3LINE_SVG,
                ),
              )
            ],
          ),
          floatingActionButton: AddTicketBtn(
            onTicketCreated: (ticketEntity) =>
                _onTicketCreated(context, ticketEntity),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          body: Column(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: TabBar(
                  controller: tabController,
                  tabs: tabs
                      .map(
                        (e) => Tab(
                          text:
                              value.homeTabEntity.mapTicketHomeTabsToString(e),
                        ),
                      )
                      .toList(),
                  isScrollable: true,
                  padding: const EdgeInsets.all(0),
                  labelPadding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
                  indicatorColor: GPColor.functionAccentWorkSecondary,
                  labelColor: GPColor.functionAccentWorkSecondary,
                  labelStyle: textStyle(GPTypography.headingSmall),
                  unselectedLabelStyle: textStyle(GPTypography.headingSmall),
                  unselectedLabelColor: GPColor.contentSecondary,
                  onTap: (value) {
                    // Dismiss keyboard when switching tabs
                    FocusScope.of(context).unfocus();
                  },
                ),
              ),
              Divider(
                color: GPColor.contentTertiary,
                height: 0.5,
              ),
              Expanded(
                child: TabBarView(
                  controller: tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: tabs
                      .map(
                        (e) => HomeListView(
                          tabEntity: value.homeTabEntity,
                          homeTab: e,
                          reloadDataCallBack: (inputData) {
                            if (inputData.length == 2) {
                              _onTicketReloadData(
                                context: context,
                                homeMenu: inputData.first,
                                homeTab: inputData.last,
                              );
                            }
                          },
                        ),
                      )
                      .toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
