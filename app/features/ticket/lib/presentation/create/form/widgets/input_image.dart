// ignore_for_file: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/lib.dart';
import 'package:gp_shared/data/model/attachment/attachment.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:gp_shared/widgets/attachment/attachment.dart';

typedef _E = GPAttachmentFileEntity;

// ignore: must_be_immutable
final class TicketCreateInputImageWidget
    extends TicketCreateInputBaseInputWrapperWidget<
        TicketCreatePickerParams<_E>> {
  TicketCreateInputImageWidget({
    super.key,
    required super.params,
    required super.inputBehavior,
    super.readyOnlyWidget,
    this.isCreatingTicket = false,
  }) {
    inputWidget = _TicketCreateInputImageWidget(
      inputBehavior: inputBehavior,
      params: params,
      isCreatingTicket: isCreatingTicket,
    );
  }

  final bool isCreatingTicket;
}

class _TicketCreateInputImageWidget extends TicketCreateBasePickerWrapperWidget<
    _E, TicketCreatePickerParams<_E>> {
  const _TicketCreateInputImageWidget({
    required super.inputBehavior,
    required super.params,
    required super.isCreatingTicket,
  });

  @override
  State<StatefulWidget> createState() => _TicketCreateInputImageWidgetState();
}

class _TicketCreateInputImageWidgetState
    extends TicketCreateBaseAttachmentPickerWrapperState<
        _TicketCreateInputImageWidget,
        TicketCreatePickerParams<_E>,
        _E> implements TicketCreatePickerBehavior<_E> {
  @override
  bool get canEdit =>
      widget.params.permissions?.isFieldCanEdit(
          isCreatingTicket: widget.isCreatingTicket, hasValue: hasInitData) ==
      true;

  @override
  void initState() {
    super.initState();
    hasInitData = params.data.isNotEmpty;
  }

  void onRemove(_E item) {
    if (!canEdit) return;
    params.data.remove(item);

    rxParams.value.data.remove(item);
    rxParams.notifyListeners();

    ticketEditDeletedIds.add(item.id);
  }

  @override
  void didUpdateWidget(covariant _TicketCreateInputImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    rxParams.value.isReadOnly = widget.params.isReadOnly;
    rxParams.value.title = widget.params.title;
    rxParams.value.subTitle = widget.params.subTitle;

    if (oldWidget.params != widget.params) {
      params = widget.params;
      // params.data
      //   ..clear()
      //   ..addAll(widget.params.data);
      rxParams.value = widget.params;
      rxParams.notifyListeners();
    }
  }

  @override
  Future<List<_E>> onAddData() async {
    final Completer<List<_E>> completer = Completer();

    await Popup.instance.showBottomSheet(
      FilePickerBottomSheet(
        fileTypes: const [
          FileType.media,
          FileType.image,
          FileType.video,
        ],
        onSelected: (fileType) async {
          var pickerResult = await GPPicker.instance.pick(fileType);
          SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);

          if (pickerResult != null) {
            final attachmentEntities = attachmentEntitiesFromPicker(
              pickerResult,
              fileType,
              uploadCallback,
            );
            params.data.addAll(attachmentEntities);

            rxCurrentValidateMode.value =
                BaseTicketInputValidateMode.validateFromAnotherAction;

            rxParams.value = params;
            rxParams.notifyListeners();
          }

          completer.complete(params.data);
        },
      ),
    );

    hideKeyboard();

    return completer.future;
  }

  @override
  Widget buildDataWidget(List<_E> data, int displayCount) {
    int newDisplayCount = displayCount;
    final dataLength = data.length;

    if (dataLength < displayCount) {
      newDisplayCount = dataLength;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildWhenHasNoDataWidget(),
        const SizedBox(height: 8),
        _MediasWidget(
          isReadOnly: widget.params.isReadOnly,
          data: data,
          displayCount: newDisplayCount,
          onRemoveFunc: onRemove,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // temp
    // rxParams.value = widget.params;
    return ValueListenableBuilder(
      valueListenable: rxParams,
      builder: (context, value, child) {
        return widget.params.permissions?.canView == true
            ? TicketCreateBasePickerWidget(
                inputBehavior: widget.inputBehavior,
                attachmentBehavior: this,
                params: value,
                listViewType: TicketBaseAttachmentListViewType.grid,
                isCreatingTicket: widget.isCreatingTicket,
                hasInitData: hasInitData,
              )
            : const SizedBox();
      },
    );
  }
}

class _MediasWidget extends StatelessWidget {
  const _MediasWidget({
    this.isReadOnly = false,
    required this.data,
    required this.displayCount,
    required this.onRemoveFunc,
  });

  final bool isReadOnly;

  final List<_E> data;
  final int displayCount;
  final Function(_E item) onRemoveFunc;

  void onRemove(_E item) {
    data.remove(item);

    onRemoveFunc.call(item);
  }

  void goToMediaPreviewList(int index) async {
    final List<GMedia> medias = data.map((e) {
      if (e.type == GPAttachmentFileUploadType.video) {
        // Web upload lên src sẽ như này: "/assets/images/chat/video.svg"??
        // phải update lại mới mở đc
        e.src = e.fileLink ?? '';
        return e.toGMedia();
      }
      return e.toGMedia();
    }).toList();
    Utils.openMediaPreview(
      medias,
      initialIndex: index,
      viewMultiple: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final itemWidth = MediaQuery.of(context).size.width / 4 - 16;

    return isReadOnly && data.isEmpty
        ? const SizedBox()
        : GridView.builder(
            padding: const EdgeInsets.all(0),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayCount,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 1,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              mainAxisExtent: itemWidth,
            ),
            itemBuilder: (context, index) {
              final item = data[index];
              final previewSize = GpAttachmentFilePreviewSize(
                width: itemWidth,
                height: itemWidth,
              );

              if (item.type == GPAttachmentFileUploadType.video) {
                item.src = item.fileLink ?? '';
              }

              return Stack(
                children: [
                  InkWell(
                    onTap: () => goToMediaPreviewList(index),
                    child: GPAttachmentFilePreview(
                      item: item,
                      previewSize: previewSize,
                    ),
                  ),
                  if (!isReadOnly)
                    Positioned(
                      right: 4,
                      top: 4,
                      child: InkWell(
                        customBorder: const CircleBorder(),
                        onTap: () => onRemove(item),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: GPColor.bgPrimary,
                            border: Border.all(
                              color: GPColor.lineTertiary,
                            ),
                          ),
                          child: SvgWidget(
                            Assets
                                .PACKAGES_GP_ASSETS_IMAGES_SVG_IC12_FILL_XMARK_SVG,
                            color: GPColor.contentSecondary,
                          ),
                        ),
                      ),
                    )
                ],
              );
            },
          );
  }
}
