import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/check_in_status.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/controller/time_keeping_controller.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/controller/work_shift_item_controller.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/widgets/check_in_widget.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/widgets/check_out_widget.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_model_ver2.dart';

class WorkShiftItemView extends StatelessWidget {
  WorkShiftItemView({
    super.key,
    required this.shiftItem,
    required this.timeKeepingController,
  });

  final ShiftModelVer2 shiftItem;

  final TimeKeepingControllerVer2 timeKeepingController;

  final expandableController = ExpandableController(initialExpanded: true);

  @override
  Widget build(BuildContext context) {
    // <PERSON>hi chỉ có ca chưa được chỉ định, không hiển thị Not assign
    final bool hasSingleNoShift =
        timeKeepingController.listShiftToday.length == 1 && shiftItem.isNoShift;
    return ColoredBox(
      color: GPColor.bgPrimary,
      child: Column(children: [
        if (!hasSingleNoShift)
          _TitleItem(
            shift: shiftItem,
            controller: timeKeepingController,
          ),
        // shift detail in formation
        ExpandableNotifier(
          controller: expandableController,
          child: Expandable(
            collapsed: const SizedBox(),
            expanded: _ShiftDetailInfo(
                timeKeepingController: timeKeepingController,
                shiftItem: shiftItem),
          ),
        ),
      ]),
    );
  }
}

class _ShiftDetailInfo extends StatelessWidget {
  _ShiftDetailInfo({
    required this.timeKeepingController,
    required this.shiftItem,
  }) {
    shiftController = WorkShiftItemController();
  }
  final TimeKeepingControllerVer2 timeKeepingController;

  final ShiftModelVer2 shiftItem;

  late final WorkShiftItemController shiftController;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TimeKeepingControllerVer2>(
        global: false,
        init: timeKeepingController,
        id: KeyUpdate.updateExpanded,
        builder: (_) {
          shiftController.initData(shiftItem, timeKeepingController);

          return (shiftItem.isExpanded ?? false)
              ? Column(
                  children: [
                    Obx(
                      () {
                        if (shiftController.rxCheckInShiftId?.isEmpty == true) {
                          return const SizedBox();
                        }
                        return Column(
                          children: [
                            shiftController.checkInStatus ==
                                    CheckInOutStatus.tooSoon
                                ? const _LabelHideCheckIn()
                                : const SizedBox(),
                            CheckInWidget(
                              controller: shiftController,
                              isHasRequest: shiftController
                                  .workingShift.value.isShiftApproved,
                            )
                          ],
                        );
                      },
                    ),
                    Obx(
                      () {
                        if (shiftController.rxCheckOutShiftId?.isEmpty == true) {
                          return const SizedBox();
                        }
                        return CheckOutWidget(
                          controller: shiftController,
                          isHasRequest: shiftController
                              .workingShift.value.isShiftApproved,
                        );
                      },
                    ),
                    // GetBuilder<WorkShiftItemController>(
                    //   global: false,
                    //   id: "ToanNMDev",//'${KeyUpdate.updateStatusCheckIn}/${shiftItem.id}'
                    //   init: shiftController,
                    //   builder: (_) {
                    //     logDebug(
                    //         'checkin shiftController -> ${_.hashCode}');
                    // return Column(
                    //   children: [
                    //     shiftController.checkInStatus ==
                    //             CheckInOutStatus.tooSoon
                    //         ? const _LabelHideCheckIn()
                    //         : const SizedBox(),
                    //     CheckInWidget(
                    //       controller: shiftController,
                    //       isHasRequest: shiftController
                    //           .workingShift.value.isShiftApproved,
                    //     )
                    //   ],
                    // );
                    //   },
                    // ),
                    // GetBuilder<WorkShiftItemController>(
                    //   global: false,
                    //   id: '${KeyUpdate.updateStatusCheckOut}/${shiftItem.id}',
                    //   init: shiftController,
                    //   builder: (_) {
                    //     logDebug(
                    //         'checkout shiftController ->  ${_.hashCode}');
                    // return CheckOutWidget(
                    //   controller: shiftController,
                    //   isHasRequest: shiftController
                    //       .workingShift.value.isShiftApproved,
                    // );
                    //   },
                    // ),
                  ],
                )
              : const SizedBox();
        });
  }
}

class _TitleItem extends StatelessWidget {
  const _TitleItem({
    required this.shift,
    required this.controller,
  });

  final ShiftModelVer2 shift;

  final TimeKeepingControllerVer2 controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(color: GPColor.lineTertiary, width: 1))),
      child: IntrinsicHeight(
        child: Row(children: [
          GetBuilder<TimeKeepingControllerVer2>(
            global: false,
            init: controller,
            id: KeyUpdate.updateExpanded,
            builder: (_) => VerticalDivider(
              width: 4,
              thickness: 4,
              color: shift.isExpanded ?? false
                  ? GPColor.functionAccentWorkSecondary
                  : GPColor.bgPrimary,
            ),
          ),
          Expanded(
            child: Text(
              shift.name,
              style: textStyle(GPTypography.headingSmall)?.copyWith(
                color: GPColor.contentPrimary,
                fontWeight: FontWeight.w600,
                height: 20 / 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ).paddingSymmetric(horizontal: 12, vertical: 13),
          ),
          const SizedBox(
            width: 10,
          ),
          GetBuilder<TimeKeepingControllerVer2>(
            global: false,
            init: controller,
            id: KeyUpdate.updateExpanded,
            builder: (_) => shift.isExpanded ?? false
                ? const SizedBox()
                : Row(
                    children: [
                      Text(
                        shift.shiftDurationRangeText,
                        style: textStyle(GPTypography.bodyMedium)?.copyWith(
                          color: GPColor.contentSecondary,
                          height: 20 / 14,
                        ),
                      ).paddingSymmetric(horizontal: 16, vertical: 12),
                      shift.isNoShift
                          ? SvgWidget(
                              Assets
                                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PERSON_XMARK_SVG,
                              color: GPColor.contentSecondary,
                              width: 20,
                              height: 20,
                            ).paddingOnly(right: 16)
                          : const SizedBox(),
                      (shift.fixedWorkingHour?.isOvernight ?? false)
                          ? const SvgWidget(
                              'packages/gp_assets/images/shift-overnight.svg',
                              width: 24,
                              height: 24,
                            ).paddingOnly(right: 16)
                          : const SizedBox()
                    ],
                  ),
          )
        ]),
      ),
    );
  }
}

class _LabelHideCheckIn extends StatelessWidget {
  const _LabelHideCheckIn();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgWidget(
          "assets/images/ic16-fill-clock.svg",
          color: GPColor.functionCriticalPrimary,
        ),
        Text(LocaleKeys.timeKeeping_unavailableCheckIn.tr,
                style: textStyle(GPTypography.bodySmall)?.copyWith(
                    color: GPColor.functionCriticalPrimary, height: 16 / 12))
            .paddingSymmetric(horizontal: 8)
      ],
    ).paddingSymmetric(horizontal: 16, vertical: 12);
  }
}
