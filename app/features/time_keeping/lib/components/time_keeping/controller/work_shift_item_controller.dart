import 'dart:async';

import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/check_in_status.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/controller/time_keeping_controller.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/controller/work_shift_item_controller_ext/check_in_out_mixin.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/widgets/bottom_sheet_check_in_out_success.dart';
import 'package:gp_feat_time_keeping/model/request/check_in_out_request.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/attendance_model.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/enum/attendance_type.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_model_ver2.dart';

import 'base_work_shift_item_controller.dart';

class WorkShiftItemController extends BaseWorkShiftItemController {
  WorkShiftItemController();

  /// var set ui
  final Rx<String> timeNow = DateFormat.jms().format(DateTime.now()).obs;

  // var work shift
  final Rx<ShiftModelVer2> workingShift = ShiftModelVer2(id: 'id').obs;
  DateTime currentTime = DateTime.now();

  late TimeKeepingControllerVer2 timeKeepingControllerVer2;

  @override
  String get shiftId => workingShift.value.id;

  void initData(
      ShiftModelVer2 shift, TimeKeepingControllerVer2 timeKeepingController) {
    timeKeepingControllerVer2 = timeKeepingController;

    workingShift.value = shift;
    checkInStatus = getCheckInStatus(shift, currentTime);
    checkOutStatus = getCheckOutStatus(shift, currentTime);

    rxCheckInShiftId = shift.id.obs;
    rxCheckOutShiftId = shift.id.obs;

    // count time
    Timer.periodic(
        const Duration(seconds: 1), (Timer timer) => _updateCurrentTime());
  }

  int getShiftIndex() {
    for (int i = 0; i < timeKeepingControllerVer2.listShiftToday.length; i++) {
      ShiftModelVer2 shift = timeKeepingControllerVer2.listShiftToday[i];
      if (shift.id == workingShift.value.id &&
          shift.shiftOnDateId == workingShift.value.shiftOnDateId) {
        return i;
      }
    }
    return 0;
  }

  Future onClickCheckIn({
    String? urlImage,
  }) async {
    const attendenceType = AttendanceType.checkIn;
    if (shiftId == '' || shiftId == '--') {
      return onClickCheckInWithoutShift(attendenceType);
    }

    CheckInOutRequest? requestParams;

    Future checkIn(CheckInOutRequest request) async {
      final response = await timeKeepingUseCase.checkIn(request);
      final pickImageModels = request.pickImageModels;

      AttendanceModel attendance = AttendanceModel.fromJson(response.toJson());
      attendance.type = attendenceType;
      attendance.images = pickImageModels?.map((e) => e.uploadedSrc).toList();

      attendance.xFiles?.clear();
      attendance.xFiles = pickImageModels?.map((e) => e.xFile).toList();

      await onSuccessCheckInOut(attendance: attendance);

      GPCoreTracker().appendCheckin(
        'Flutter:timeKeeping.checkInSuccess',
        data: {
          'request.shiftId': request.shiftId,
          'request.shiftOnDateId': request.shiftOnDateId,
          'time': attendance.time,
          'imageUrl': attendance.images,
          'attendance': attendance.toLog(),
        },
      );
    }

    CheckInOutCallBack checkInCallBack = CheckInOutCallBack(onLoading: () {
      onLoadingCheckInOut();
    }, onEmptyData: () {
      onEmptyDataCheckInOut();
    }, onRequestCheckInOut: (CheckInOutRequest checkInRequest) async {
      requestParams = checkInRequest;

      await checkIn(checkInRequest);
    }, onHandleError: (Object e, StackTrace s) async {
      await handleCheckInOutErrors(
        e,
        s,
        ({pickImageModels, office}) async {
          if (requestParams != null) {
            requestParams?.office = office;
            requestParams?.pickImageModels = pickImageModels;

            try {
              return await checkIn(requestParams!);
            } catch (e, s) {
              handleError(e, s);
            }
          }
        },
      );
    });

    if (isLoading.value) return;

    return await onClickCheckInOut(checkInCallBack, shift: workingShift.value);
  }

  Future onClickCheckOut() async {
    const attendenceType = AttendanceType.checkOut;
    if (shiftId == '' || shiftId == '--') {
      return onClickCheckInWithoutShift(attendenceType);
    }

    CheckInOutRequest? requestParams;

    Future checkOut(CheckInOutRequest request) async {
      final response = await timeKeepingUseCase.checkOut(request);
      final pickImageModels = request.pickImageModels;

      AttendanceModel attendance = AttendanceModel.fromJson(response.toJson());
      attendance.type = attendenceType;
      attendance.images = pickImageModels?.map((e) => e.uploadedSrc).toList();

      attendance.xFiles?.clear();
      attendance.xFiles = pickImageModels?.map((e) => e.xFile).toList();

      await onSuccessCheckInOut(attendance: attendance);

      GPCoreTracker().appendCheckout(
        'Flutter:timeKeeping.checkOutSuccess',
        data: {
          'request.shiftId': request.shiftId,
          'request.shiftOnDateId': request.shiftOnDateId,
          'time': attendance.time,
          'imageUrl': attendance.images,
          'attendance': attendance.toLog(),
        },
      );
    }

    CheckInOutCallBack checkOutCallBack = CheckInOutCallBack(onLoading: () {
      onLoadingCheckInOut();
    }, onEmptyData: () {
      onEmptyDataCheckInOut();
    }, onRequestCheckInOut: (CheckInOutRequest checkOutRequest) async {
      requestParams = checkOutRequest;

      await checkOut(checkOutRequest);
    }, onHandleError: (Object e, StackTrace s) async {
      await handleCheckInOutErrors(
        e,
        s,
        ({pickImageModels, office}) async {
          if (requestParams != null) {
            requestParams?.office = office;
            requestParams?.pickImageModels = pickImageModels;

            return await checkOut(requestParams!);
          }
        },
      );
    });

    if (isLoading.value) return;

    return await onClickCheckInOut(checkOutCallBack, shift: workingShift.value);
  }

  void _updateCurrentTime() {
    timeNow.value = DateFormat.jms().format(DateTime.now());
  }

  Future reloadListShift() async {
    return await timeKeepingControllerVer2.fetchListWorkingShiftToday();
  }

  @override
  void onLoadingCheckInOut() {
    isLoading.value = true;
    timeKeepingControllerVer2.isCheckingInOut = true;
  }

  @override
  void onEmptyDataCheckInOut() {
    isLoading.value = false;
  }

  @override
  Future reloadList() async {
    isLoading.value = false;
    errorCheckInOut.value = '';
    await reloadListShift();
    timeKeepingControllerVer2.expandShift(getShiftIndex(), isReloadList: true);
  }

  @override
  Future onSuccessCheckInOut({required AttendanceModel attendance}) async {
    isLoading.value = false;
    errorCheckInOut.value = '';
    timeKeepingControllerVer2.isCheckingInOut = false;
    if (attendance.attendanceType == AttendanceType.checkIn) {
      workingShift.value.attendances = [attendance];
      checkInStatus = CheckInOutStatus.checkInSuccess;
      checkOutStatus = CheckInOutStatus.allowedCheckIn;
      updateCheckInStatus();
      updateCheckOutStatus();
      Popup.instance.showBottomSheet(
        BottomSheetCheckInOutSuccess(attendance: attendance, isCheckIn: true),
      );
    } else {
      workingShift.value.attendances?.insert(0, attendance);
      updateCheckOutStatus();
      Popup.instance.showBottomSheet(
        BottomSheetCheckInOutSuccess(attendance: attendance, isCheckIn: false),
      );
    }
  }
}
