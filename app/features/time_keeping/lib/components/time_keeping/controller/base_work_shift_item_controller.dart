import 'dart:async';

import 'package:flutter/material.dart' as material;
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_time_keeping/components/components.dart';
import 'package:gp_feat_time_keeping/model/request/check_in_out_request.dart';
import 'package:gp_feat_time_keeping/model/response/office_response.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/attendance_model.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/enum/attendance_type.dart';

import '../../../model/response/error_code_enum.dart';

abstract class BaseWorkShiftItemControllerBehavior {
  String get shiftId;

  Future reloadList();

  void onLoadingCheckInOut();

  void onEmptyDataCheckInOut();

  Future onSuccessCheckInOut({required AttendanceModel attendance});
}

abstract class BaseWorkShiftItemController extends BaseController
    with
        LocationPermission,
        CameraPermission,
        CheckInOutMixin,
        _BottomSheetMixin
    implements BaseWorkShiftItemControllerBehavior {
  BaseWorkShiftItemController({
    GPConnection? gpConnection,
  }) : super(gpConnection ?? GPConnectionConcrete());

  /// var set ui
  final Rx<String> errorCheckInOut = ''.obs;
  CheckInOutStatus checkInStatus = CheckInOutStatus.allowedCheckIn;
  CheckInOutStatus checkOutStatus = CheckInOutStatus.allowedCheckIn;

  // var usecase + api
  final TimeKeepingApi timeKeepingApi = TimeKeepingApi();
  late final TimeKeepingUseCase timeKeepingUseCase =
      TimeKeepingUseCase(timeKeepingApi);

  /*
    Dùng GetBuild, listen theo shift_id của từng loại checkin, checkout nhưng không ăn
  */

  RxString? rxCheckInShiftId;
  RxString? rxCheckOutShiftId;

  void updateCheckInStatus() {
    // update(['${KeyUpdate.updateStatusCheckIn}/$shiftId']);
    rxCheckInShiftId?.value = shiftId;

    rxCheckInShiftId?.refresh();
  }

  void updateCheckOutStatus() {
    // update(['${KeyUpdate.updateStatusCheckOut}/$shiftId']);
    rxCheckOutShiftId?.value = shiftId;

    rxCheckOutShiftId?.refresh();
  }

  Future onClickCheckInWithoutShift(AttendanceType attendanceType) async {
    CheckInOutRequest? requestParams;

    Future checkInWithoutShift(CheckInOutRequest request) async {
      final response = await timeKeepingUseCase.checkInWithoutShift(request);

      final pickImageModels = request.pickImageModels;

      AttendanceModel attendance = AttendanceModel.fromJson(response.toJson());
      attendance.type = attendanceType;
      attendance.images = pickImageModels?.map((e) => e.uploadedSrc).toList();

      attendance.xFiles?.clear();
      attendance.xFiles = pickImageModels?.map((e) => e.xFile).toList();

      await onSuccessCheckInOut(attendance: attendance);

      GPCoreTracker().appendCheckout(
        'Flutter:timeKeeping.checkOutSuccess',
        data: {
          'request.shiftId': request.shiftId,
          'request.shiftOnDateId': request.shiftOnDateId,
          'time': attendance.time,
          'imageUrl': attendance.images,
          'attendance': attendance.toLog(),
        },
      );

      logDebug('onClickCheckInWithoutShift response -> $response');
    }

    CheckInOutCallBack checkInCallBack = CheckInOutCallBack(onLoading: () {
      onLoadingCheckInOut();
    }, onEmptyData: () {
      onEmptyDataCheckInOut();
    }, onRequestCheckInOut: (CheckInOutRequest request) async {
      requestParams = request;

      await checkInWithoutShift(request);
    }, onHandleError: (Object e, StackTrace s) async {
      await handleCheckInOutErrors(
        e,
        s,
        ({pickImageModels, office}) async {
          if (requestParams != null) {
            requestParams?.office = office;
            requestParams?.pickImageModels = pickImageModels;

            try {
              return await checkInWithoutShift(requestParams!);
            } catch (e, s) {
              handleError(e, s);
            }
          }
        },
      );
    });

    if (isLoading.value) return;

    return await onClickCheckInOut(checkInCallBack, shift: null);
  }

  @override
  void handleError(Object error, s) async {
    logDebug("got error ${error.toString()}, $s");
    isLoading.value = false;
    if (!(await gpConnection.isInternetConnected())) {
      errorCheckInOut.value = LocaleKeys.timeKeeping_errorWifiConnection.tr;

      GPCoreTracker().appendError(
        'Flutter:timeKeeping.handleError: gpConnection.isInternetConnected() == false',
        data: {'errorCheckInOut.value': errorCheckInOut.value},
      );
    } else {
      await _parseErrorCheckInOut(error);
    }
  }

  Future _parseErrorCheckInOut(Object e) async {
    if (e is AppException) {
      // check mã lỗi
      int? errorCode = e.response?.code;
      if (errorCode == null) {
        GPCoreTracker().appendError(
          'Flutter:timeKeeping._parseErrorCheckInOut: return',
          data: {'errorCode': errorCode},
        );
        return;
      }

      if (errorCode == TimeKeepingErrorEnum.e400101.code) {
        errorCheckInOut.value =
            e.message ?? LocaleKeys.timeKeeping_popup_400101.tr;

        GPCoreTracker().appendError(
          'Flutter:timeKeeping._parseErrorCheckInOut: $errorCode',
          data: {'errorCode': errorCode},
        );
        return;
      } else if (errorCode == TimeKeepingErrorEnum.e400102.code) {
        errorCheckInOut.value =
            e.message ?? LocaleKeys.timeKeeping_popup_400102.tr;

        GPCoreTracker().appendError(
          'Flutter:timeKeeping._parseErrorCheckInOut: $errorCode',
          data: {'errorCode': errorCode},
        );
        return;
      }

      switch (errorCode) {
        // chấm công vào lần thứ 2 trở đi -> show snack bar thông báo
        case 400021:
          GPCoreTracker().appendError(
            'Flutter:timeKeeping._parseErrorCheckInOut: handle 400021',
            data: {'errorCode': errorCode},
          );
          errorCheckInOut.value = '';

          checkInStatus = CheckInOutStatus.checkInSuccess;
          checkOutStatus = CheckInOutStatus.allowedCheckIn;
          updateCheckInStatus();
          updateCheckOutStatus();

          return await showSnackBarCheckInSuccessed(onReload: reloadList);
        // quá giờ chấm công vào/ra -> show bottom sheet thông báo + reload list shift
        case 400015:
        case 400017:
          GPCoreTracker().appendError(
            'Flutter:timeKeeping._parseErrorCheckInOut: handle 400015 or 400017',
            data: {'errorCode': errorCode},
          );
          errorCheckInOut.value = '';

          return await showBottomSheet(onReload: reloadList);
        // yêu cầu chấm công với hình ảnh -> mở camera và chấm công lại
        // case 400008:
        // return await onCheckInOutWithImage();
        // mặc định show lỗi phía dưới thông tin ca làm việc
        case 400030:
        case 400031:
        case 400032:
          GPCoreTracker().appendError(
            'Flutter:timeKeeping._parseErrorCheckInOut: handle 400030 or 400031 or 400032',
            data: {'errorCode': errorCode},
          );
          errorCheckInOut.value = '';

          return await showBottomSheetPermissionError(errorCode);

        default:
          if (e.toString().isNotEmpty) {
            errorCheckInOut.value = e.toString();
          }

          GPCoreTracker().appendError(
            'Flutter:timeKeeping._parseErrorCheckInOut: handle default',
            data: {
              'errorCode': errorCode,
              'errorCheckInOut.value': errorCheckInOut.value
            },
          );

          break;
      }

      GPCoreTracker().sendLog(
        message: 'GPCoreTracker error: $errorCode',
      );
    } else if (e is TimeoutException && e.message == 'location') {
      GPCoreTracker().appendError(
        'Flutter:timeKeeping._parseErrorCheckInOut: showBottomSheetLocationError',
      );
      errorCheckInOut.value = '';

      showBottomSheetLocationError();
    } else {
      errorCheckInOut.value = LocaleKeys.error_default_msg.tr;

      GPCoreTracker().appendError(
        'Flutter:timeKeeping._parseErrorCheckInOut: nothing:',
        data: {'errorCheckInOut.value': '${errorCheckInOut.value}, \nex: $e'},
      );
    }
  }

  Future handleCheckInOutErrors(
    Object e,
    StackTrace s,
    Future Function({
      List<PickImageModel>? pickImageModels,
      OfficeResponse? office,
    }) callback,
  ) async {
    isLoading.value = false;

    if (e is AppException) {
      if (e.response?.code == 400008) {
        final PickImageModel? pickImageModels = await openCameraAndPickImage();
        OfficeResponse? office;

        try {
          final data = e.response?.data;
          if (data != null) {
            if (data.containsKey('office')) {
              office = OfficeResponse.fromJson(data['office']);
            } else if (data.containsKey('office_id')) {
              office = OfficeResponse(
                id: data['office_id'],
              );
            }
          }
        } catch (ex) {
          GPCoreTracker().appendError(
            'Flutter:timeKeeping.handleCheckInOutErrors: error:',
            data: {'exception': '$ex'},
          );
          logDebug('$tagDebug: handleCheckInOutErrors: $ex');
        }

        if (pickImageModels != null) {
          return await callback(
            pickImageModels: [pickImageModels],
            office: office,
          );
        }
      } else {
        handleError(e, s);
      }
    } else {
      handleError(e, s);
    }
  }
}

mixin _BottomSheetMixin {
  Future showBottomSheet({required Function() onReload}) async {
    await Popup.instance.showBottomSheet(BottomSheetMissedCheckInOutWidget(
      onTap: () async {
        Get.back();
        onReload;
      },
    ));

    GPCoreTracker().appendMessage(
      'Flutter:timeKeeping._parseErrorCheckInOut: showBottomSheet',
      data: {
        'content': LocaleKeys.timeKeeping_outOfCheckInTime.tr,
      },
    );
  }

  Future showBottomSheetPermissionError(int code) async {
    String title = '';
    String description = '';
    switch (code) {
      // Không thể chấm công vì quản trị viên đã tắt hoạt động trên thiết bị của bạn
      case 400030:
        title = LocaleKeys.timeKeeping_permission_disable_device.tr;
        description =
            LocaleKeys.timeKeeping_permission_disable_device_descrition.tr;
        break;
      // Không thể chấm công vì bạn đang sử dụng chung thiết bị với người khác
      case 400031:
        title = LocaleKeys.timeKeeping_permission_use_other_device.tr;
        description =
            LocaleKeys.timeKeeping_permission_use_other_device_description.tr;
        break;
      // Không thể chấm công vì bạn đang sử dụng thiết bị khác với thiết bị được đăng ký
      case 400032:
        title = LocaleKeys.timeKeeping_permission_unregister_device.tr;
        description =
            LocaleKeys.timeKeeping_permission_unregister_device_description.tr;
        break;
      default:
    }
    await Popup.instance.showBottomSheet(
        BottomSheetCheckInOutError(title: title, description: description));

    GPCoreTracker().appendMessage(
      'Flutter:timeKeeping._parseErrorCheckInOut: showBottomSheetPermissionError',
      data: {
        'title': title,
        'description': description,
      },
    );
  }

  Future showBottomSheetLocationError() async {
    await Get.bottomSheet(
      // wrap để auto height
      const BottomSheetCanNotGetLocationError(),
      shape: const material.RoundedRectangleBorder(
        borderRadius: material.BorderRadius.only(
          topLeft: material.Radius.circular(16),
          topRight: material.Radius.circular(16),
        ),
      ),
      isScrollControlled: true,
      backgroundColor: material.Colors.transparent,
      isDismissible: true,
      enableDrag: true,
    );

    GPCoreTracker().appendMessage(
      'Flutter:timeKeeping.checkInSuccess.error._parseErrorCheckInOut: showBottomSheetLocationError',
      data: {
        'title': LocaleKeys.timeKeeping_cantGetLocationTitle.tr,
        'description': LocaleKeys.timeKeeping_cantGetLocationDescription.tr,
      },
    );
  }

  Future showSnackBarCheckInSuccessed({required Function() onReload}) async {
    Popup.instance.showSnackBar(
      message: LocaleKeys.timeKeeping_checkInSuccessed.tr,
      type: SnackbarType.success,
    );
    onReload();
  }
}
