import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'pattern_background.dart';

/// Example implementations of the PatternBackground widget
/// showing different use cases and configurations
class PatternBackgroundExamples extends StatelessWidget {
  const PatternBackgroundExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pattern Background Examples'),
        backgroundColor: GPColor.bgPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('1. Message Pattern Background (Like Figma)'),
            _buildMessageExample(),
            const SizedBox(height: 32),
            _buildSectionTitle('2. Custom Pattern Background'),
            _buildCustomPatternExample(),
            const SizedBox(height: 32),
            _buildSectionTitle('3. Different Colors & Opacity'),
            _buildColorVariationsExample(),
            const SizedBox(height: 32),
            _buildSectionTitle('4. Different Sizes'),
            _buildSizeVariationsExample(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: textStyle(GPTypography.headingLarge)?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildMessageExample() {
    return Container(
      height: 80,
      width: double.infinity,
      child: MessagePatternBackground(
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgWidget(
                'assets/images/svg/ic16-fill-informationmark-circle.svg',
                color: GPColor.orangeDarker,
                width: 16,
                height: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Chưa đồng bộ với lịch Google.',
                style: textStyle(GPTypography.headingSmall)?.copyWith(
                  color: GPColor.orangeDarker,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Đồng bộ ngay',
                style: textStyle(GPTypography.headingSmall)?.copyWith(
                  color: GPColor.orangeDarker,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomPatternExample() {
    return Container(
      height: 120,
      width: double.infinity,
      child: PatternBackground(
        color: GPColor.blue,
        opacity: 0.05,
        child: Container(
          color: GPColor.blueLight.withValues(alpha: 0.3),
          child: Center(
            child: Text(
              'Custom Pattern with Blue Theme',
              style: textStyle(GPTypography.headingMedium)?.copyWith(
                color: GPColor.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildColorVariationsExample() {
    return Column(
      children: [
        // Red variation
        Container(
          height: 60,
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 8),
          child: PatternBackground(
            color: GPColor.functionNegativePrimary,
            opacity: 0.03,
            child: Container(
              color: GPColor.functionNegativePrimary.withValues(alpha: 0.1),
              child: Center(
                child: Text(
                  'Red Pattern Background',
                  style: textStyle(GPTypography.bodyLarge)?.copyWith(
                    color: GPColor.functionNegativePrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Green variation
        Container(
          height: 60,
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 8),
          child: PatternBackground(
            color: GPColor.functionPositivePrimary,
            opacity: 0.04,
            child: Container(
              color: GPColor.functionPositivePrimary.withValues(alpha: 0.1),
              child: Center(
                child: Text(
                  'Green Pattern Background',
                  style: textStyle(GPTypography.bodyLarge)?.copyWith(
                    color: GPColor.functionPositivePrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Purple variation
        Container(
          height: 60,
          width: double.infinity,
          child: PatternBackground(
            color: GPColor.purple,
            opacity: 0.025,
            child: Container(
              color: GPColor.purple.withValues(alpha: 0.1),
              child: Center(
                child: Text(
                  'Purple Pattern Background',
                  style: textStyle(GPTypography.bodyLarge)?.copyWith(
                    color: GPColor.purple,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSizeVariationsExample() {
    return Column(
      children: [
        // Small rectangles
        Container(
          height: 60,
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 8),
          child: PatternBackground(
            color: GPColor.orangeDarker,
            opacity: 0.06,
            rectangleSize: 100,
            spacing: 2,
            child: Container(
              color: GPColor.orangeLight.withValues(alpha: 0.3),
              child: Center(
                child: Text(
                  'Small Rectangles (100px)',
                  style: textStyle(GPTypography.bodyLarge)?.copyWith(
                    color: GPColor.orangeDarker,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Large rectangles
        Container(
          height: 60,
          width: double.infinity,
          child: PatternBackground(
            color: GPColor.orangeDarker,
            opacity: 0.03,
            rectangleSize: 400,
            spacing: 8,
            child: Container(
              color: GPColor.orangeLight.withValues(alpha: 0.3),
              child: Center(
                child: Text(
                  'Large Rectangles (400px)',
                  style: textStyle(GPTypography.bodyLarge)?.copyWith(
                    color: GPColor.orangeDarker,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
