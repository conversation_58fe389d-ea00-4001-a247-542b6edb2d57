import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

/// A decorative pattern background widget that creates a grid of rectangles
/// similar to the Figma design pattern. Used as a subtle background decoration.
class PatternBackground extends StatelessWidget {
  const PatternBackground({
    super.key,
    this.color,
    this.opacity = 0.02,
    this.rectangleSize = 259.51,
    this.spacing = 4.0,
    this.child,
  });

  /// The color of the pattern rectangles. Defaults to orange color from design.
  final Color? color;

  /// The opacity of the pattern. Defaults to 0.02 as per Figma design.
  final double opacity;

  /// The size of each rectangle in the pattern.
  final double rectangleSize;

  /// The spacing between rectangles.
  final double spacing;

  /// Optional child widget to display on top of the pattern.
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Pattern background
        Positioned.fill(
          child: CustomPaint(
            painter: _PatternPainter(
              color: (color ?? GPColor.orangeDarker).withValues(alpha: opacity),
              rectangleSize: rectangleSize,
              spacing: spacing,
            ),
          ),
        ),
        // Child content on top
        if (child != null) child!,
      ],
    );
  }
}

/// Custom painter that draws the grid pattern of rectangles
class _PatternPainter extends CustomPainter {
  _PatternPainter({
    required this.color,
    required this.rectangleSize,
    required this.spacing,
  });

  final Color color;
  final double rectangleSize;
  final double spacing;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Calculate how many rectangles we can fit horizontally and vertically
    final stepSize = rectangleSize + spacing;
    final horizontalCount = (size.width / stepSize).ceil() + 2;
    final verticalCount = (size.height / stepSize).ceil() + 2;

    // Start from negative offset to ensure pattern covers the entire area
    final startX = -rectangleSize;
    final startY = -rectangleSize;

    // Draw the grid of rectangles
    for (int i = 0; i < horizontalCount; i++) {
      for (int j = 0; j < verticalCount; j++) {
        final x = startX + (i * stepSize);
        final y = startY + (j * stepSize);

        final rect = Rect.fromLTWH(x, y, rectangleSize, rectangleSize);
        canvas.drawRect(rect, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _PatternPainter ||
        oldDelegate.color != color ||
        oldDelegate.rectangleSize != rectangleSize ||
        oldDelegate.spacing != spacing;
  }
}

/// A specialized pattern background for message screens with orange theme
class MessagePatternBackground extends StatelessWidget {
  const MessagePatternBackground({
    super.key,
    required this.child,
    this.backgroundColor,
  });

  final Widget child;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? GPColor.orangeLight,
      child: PatternBackground(
        color: GPColor.orangeDarker,
        opacity: 0.02,
        child: child,
      ),
    );
  }
}
