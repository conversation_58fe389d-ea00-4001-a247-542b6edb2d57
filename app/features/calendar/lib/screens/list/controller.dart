import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gp_calendar/gp_calendar.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/base/networking/base/interceptors/token_interceptor.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/shared_features/custom_repeat/utils.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_calendar/database/calendar_local_data_soucre.dart';
import 'package:gp_feat_calendar/database/outlook_token_local_data_source.dart';
import 'package:gp_feat_calendar/screens/create/components/drag_drop_confirm_notify_popup.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_event_type.dart';
import 'package:gp_feat_calendar/screens/list/components/resync_google_popup.dart';
import 'package:gp_feat_calendar/screens/list/ext/google_controller.ext.dart';
import 'package:gp_feat_calendar/screens/list/mixins/appointment.dart';
import 'package:gp_feat_calendar/services/calendar_api.dart';
import 'package:gp_feat_calendar/utils/deeplink.dart';
import 'package:msal_flutter/msal_flutter.dart';
import 'package:sliding_sheet/sliding_sheet.dart';

import 'change_view_type.dart';
import 'compare_calendars_colleagues_controller.dart';
import 'components/filter_outlook_chip.dart';
import 'components/syncing_header_view.dart';
import 'components/unsyn_outlook_popup.dart';
import 'components/unsync_or_sync_outlook_popup.dart';
import 'encode_image.dart';
import 'mixins/auto_refresh_outlook_token.dart';
import 'mixins/outlook_graphql_repository.dart';
import 'mixins/outlook_local_repository.dart';
import 'mixins/outlook_remote_repository.dart';
import 'models/calendar_data_source.dart';
import 'models/calendar_data_source_load_moreable.dart';
import 'models/event_list_request.dart';
import 'models/event_list_response.dart';
import 'models/sync_account_model.dart';

class CalendarListController extends BaseListController<Appointment>
    with
        CompareCalendarsColleaguesController,
        EncodeImage,
        OutlookLocalRepository,
        OutlookRemoteRepository,
        OutlookGraphQLRepository,
        AutoRefreshOutlookToken,
        CalendarDataSourceLoadMoreable,
        GetSingleTickerProviderStateMixin,
        AppointmentMixin {
  CalendarListController({GPConnection? gpConnection})
      : super(gpConnection ?? GPConnectionConcrete()) {
    dataSource = DataSource(this, []);
    _localDataSource = CalendarLocalDataSource();
  }

  late CalendarDataSource dataSource;

  late CalendarLocalDataSource _localDataSource;

  final CalendarAPI api = CalendarAPI();

  CalendarView currentView = CalendarView.schedule;

  CalendarListEventRequest request = CalendarListEventRequest();

  List<GPCalendarEventType> gPCalendarEventTypes = GPCalendarEventType.values;

  DateTime currentFocusDateTime = DateTime.now();

  Rx<CalendarTapDetails?> busyAppointment = Rx(null);

  Rx<double> syncingViewHeight = Rx<double>(0);

  double calendarScrollOffset = 0;

  /// Để hiện shimmer loading
  Rx<bool> isHasData = false.obs;

  bool isFirstTimeRequestFullData = true;

  /// dùng chung isLoadingCalendar ở `BaseListController` sẽ bị lỗi không thể `getListems`
  bool isLoadingCalendarCalendar = false;

  /// biến này để detect sau khi user chọn today
  /// hoặc từ nhấn vào tab bar item
  /// sẽ đc gán = false khi Calendar.onScrollChanged
  bool _isOnInitialToDaySrcollOffSet = false;

  final List<CalendarView> allowedViews = <CalendarView>[
    CalendarView.schedule,
    CalendarView.day,
    CalendarView.week,
    CalendarView.month,
  ];

  Rx<SyncingHeaderViewMode> syncMode = SyncingHeaderViewMode.doneOutlook.obs;
  DateTime? _startSyncingTime;
  final SheetController sheetController = SheetController();
  final ValueNotifier<bool> maxExtentListenable = ValueNotifier(false);

  final CalendarController calendarController =
      CalendarController(enableSyncGoogle: FeatureFlag.enableSyncGoogle);
  final _sixMonthDuration = const Duration(days: 180); //30*6
  final _yearDuration = const Duration(days: 360); //30*6

  /// Sync outlook
  final RxList<SyncOutlookAccountModel> outlookAccounts = RxList();
  List<FilterOutlookChipItem> selectedFilters =
      List.from(kDefaultFilterEventAndSource);

  String? get googleEmail {
    return calendarController.syncGoogleHandler?.account?.email ??
        createMeetingGoogleEmail;
  }

  bool isSynced = false;

  String? createMeetingGoogleEmail;

  final List<CalendarTabs> tabs = [
    CalendarTabs.calendar,
    CalendarTabs.meetingRooms
  ];

  RxBool isBookingRoom = false.obs;
  RxBool showFabMenu = true.obs;
  RxBool showGGSyncNoti = false.obs;
  double headerHeight = 96;

  late TabController tabController;

  @override
  Future<void> onInit() async {
    getConfigs();

    logWithHighlight('CalendarListController.onInit()');
    final now = DateTime.now();
    request.eventFromAt =
        now.subtract(_sixMonthDuration).millisecondsSinceEpoch;
    request.eventFromTo = now.add(_yearDuration).millisecondsSinceEpoch;
    tabController = TabController(
        length: tabs.length, vsync: this, animationDuration: Duration.zero);
    tabController.addListener(() {
      if (tabController.previousIndex != tabController.index) {
        isBookingRoom.value = tabController.index == 1 ? true : false;
        if (tabController.index == 0) {
          showFabMenu.value = true;
          refreshData();
        }
      }
    });
    registerDelegate(this);

    try {
      // Lấy icon của phòng họp vật lý
      // là async task nên gọi 1 lần ở đây
      await CalendarEventList.initLocalRoomIcon();
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.calendar.initLocalRoomIcon.error',
        data: {'error': e, 'stacktrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar.initLocalRoomIcon.error',
        trackerType: GPTrackerType.calendar,
      );

      logDebug(e);
      logDebug(trace);
    }

    //
    try {
      await fetchDataFromLocal();
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.calendar.fetchDataFromLocal.error',
        data: {'error': e, 'stacktrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar.fetchDataFromLocal.error',
        trackerType: GPTrackerType.calendar,
      );

      logDebug(e);
      logDebug(trace);
    }

    try {
      getListItems();
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.calendar.getListItems.error',
        data: {'error': e, 'stacktrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar.getListItems.error',
        trackerType: GPTrackerType.calendar,
      );

      logDebug(e);
      logDebug(trace);
    }

    if (FeatureFlag.enableSyncOutlook) {
      // Deeplink.createPublicClientApplication();
      checkMSSync();
    }
    super.onInit();
  }

  Future eventTrackSync() async {
    try {
      final trackSync = await api.eventTrackSync();
      if (trackSync["is_valid"]) {
        isSynced = true;
      } else {
        isSynced = false;
      }
    } catch (e) {
      logDebug("track sync event error $e");
    }
  }

  @override
  Future<void> getListItems({CalendarEventList? event}) async {
    trackSyncGoogle();
    eventTrackSync();
    if (isLoadingCalendarCalendar || !TokenManager.hasToken) return;
    isLoadingCalendarCalendar = true;

    try {
      // reset filter
      request.getRoom = null;
      request.type = null;
      request.source = null;

      request.ownerIds = ownerIds.isNotEmpty ? ownerIds : null;
      request.fields = CalendarListEventRequest.fieldsForGetListAPI;

      // Thêm source khi enableFilterOutlook
      final sources = selectedFilters
          .where((e) {
            if (!FeatureFlag.enableFilterOutlook) {
              if (e == FilterOutlookChipItem.outlook) {
                return false;
              }
            }
            if (!FeatureFlag.enableFilterGoogle) {
              if (e == FilterOutlookChipItem.google) {
                return false;
              }
            }
            return e.source != null;
          })
          .map<String>((e) => e.source!)
          .toList();
      if (sources.isNotEmpty) {
        request.source = sources;
      }

      // Thêm filter event và room vật lý
      if (FeatureFlag.enableFilterEvent) {
        // room vật lý
        if (selectedFilters.contains(FilterOutlookChipItem.room)) {
          request.getRoom = 1;
        }

        // filter event type
        var types = <GPCalendarEventType>[];
        if (selectedFilters.contains(FilterOutlookChipItem.meeting)) {
          types.add(GPCalendarEventType.meeting);
        }
        if (selectedFilters.contains(FilterOutlookChipItem.task)) {
          types.add(GPCalendarEventType.task);
        }
        if (selectedFilters.contains(FilterOutlookChipItem.reminder)) {
          types.add(GPCalendarEventType.reminder);
        }
        if (types.isNotEmpty) {
          final type = types.map((e) => e.value()).join(',');
          request.type = type;
        }
      }

      var response = await api.getEvents(request: request);

      // Ẩn sync header nếu có
      // Trường hợp sync quá lâu thì có thể lỗi nên check theo thời gian sync
      // trên 30s thì cho phép ẩn header khi reload data
      if (syncMode.value == SyncingHeaderViewMode.syncingOutlook ||
          syncMode.value == SyncingHeaderViewMode.syncingGoogle) {
        // Có start syncing time
        if (_startSyncingTime != null) {
          if (DateTime.now().difference(_startSyncingTime!).inSeconds > 30) {
            syncMode.value = SyncingHeaderViewMode.doneOutlook;
            closeSyncHeaderView();
            _startSyncingTime = null;
          }
        } else {
          // vì lý do nào đó mà k có startSyncingTime thì set = now
          _startSyncingTime = DateTime.now();
        }
      }

      _handleResponse(response);

      if (calendarController.view == CalendarView.schedule) {
        _localDataSource.saveToStorage(
            response, CalendarLocalDataSource.keyCurrentSection);
      }

      if (event != null) {
        calendarController.displayDate =
            DateTime.fromMillisecondsSinceEpoch(event.startAt ?? 0);
      }
    } catch (error, s) {
      logDebug("get list error $error");
      handleError(error, s);
    } finally {
      if (isHasData.value == false) {
        isHasData.value = true;
      }
      isLoadingCalendarCalendar = false;
    }
  }

  Future<void> fetchDataFromLocal() async {
    if (calendarController.view == CalendarView.schedule) {
      final events = await _localDataSource.getData<CalendarListEventResponse>(
          CalendarLocalDataSource.keyCurrentSection);

      if (events != null) {
        _handleResponse(events);
        if (isHasData.value == false) {
          isHasData.value = true;
        }
      } else {
        //do nothing
      }
    } else {
      // don't fetch cache when is not schedule
    }
  }

  void onUpdateEventTypes(List<GPCalendarEventType> eventTypes) {
    gPCalendarEventTypes = eventTypes;

    final String eventTypeStr = eventTypes
        .map((e) => e.value())
        .toList()
        .toString()
        .replaceAll(" ", ""); // dạng: [Meeting,Task,Reminder]

    request.type = eventTypeStr.substring(1, eventTypeStr.length - 1);

    getListItems();
  }

  Future<void> _handleResponse(CalendarListEventResponse response) async {
    listItem.clear();
    dataSource.appointments?.clear();

    listItem
        .addAll(response.mapToAppointments(isComparing: ownerIds.isNotEmpty));

    dataSource.appointments?.addAll(listItem);

    dataSource.notifyListeners(CalendarDataSourceAction.reset, listItem);

    // remove all events
    // if (kDebugMode && Constants.displayName() == 'Nong Hoang Thai STG') {
    //   api.deleteEvents(response.data.map((e) => e.id ?? '').toList(),
    //       EditCalendarEventRequestOption(EditCalendarEventMethod.root));
    // }
  }

  /// [visibleDateTime] : ngày hiển thị hiện tại tại lịch, thay đổi theo kiểu view ngày/tuần/tháng và khi user swipe left/right
  Future updateRequestDateTimeIfNeeded(List<DateTime> visibleDateTimes) async {
    /// view lịch biểu sẽ sử dụng `handleLoadMore` của GPCalendar trong dataSource
    if (currentView == CalendarView.schedule) return;

    DateTime visibleDateTime = visibleDateTimes[visibleDateTimes.length ~/ 2];

    DateTime currentRequest =
        DateTime.fromMillisecondsSinceEpoch(request.eventFromAt!);

    if (visibleDateTime.year != currentRequest.year ||
        isFirstTimeRequestFullData) {
      isFirstTimeRequestFullData = false;
      DateTime createdAt = DateTime(visibleDateTime.year, 1, 1);
      DateTime endAt = DateTime(visibleDateTime.year, 12, 31);

      request.eventFromAt = createdAt.millisecondsSinceEpoch;
      request.eventFromTo = endAt.millisecondsSinceEpoch;

      await getListItems();
    }
  }

  /// [visibleDateTime] : ngày hiển thị hiện tại tại lịch, thay đổi theo kiểu view ngày/tuần/tháng và khi user swipe left/right
  void updateRequestDateTime(DateTime visibleDateTime) {
    request.eventFromAt =
        visibleDateTime.subtract(_sixMonthDuration).millisecondsSinceEpoch;
    request.eventFromTo =
        visibleDateTime.add(_yearDuration).millisecondsSinceEpoch;
  }

  int _getDurationInterval() {
    switch (currentView) {
      case CalendarView.month:
        return 45;
      case CalendarView.week:
        return 14;
      default:
        return 3;
    }
  }

  Future<void> refreshData({
    bool? forceData = false,
  }) async {
    /*
      Android: 
      forceData == null -> reloađData
      forceData == false -> navigate to current
      forceData == true -> navigate to current and reload data
    */
    bool isAndroidOnResume = GetPlatform.isAndroid && forceData == null;
    bool reloadData = isAndroidOnResume || forceData == true;

    if (!isAndroidOnResume) {
      var requestDateTime = DateTime.now();
      calendarController.displayDate = requestDateTime;
      updateRequestDateTime(requestDateTime);
    }

    if (reloadData || _isOnInitialToDaySrcollOffSet) {
      isHasData.value = false;
      await getListItems();
      /*
        Android khi click vào tab bar, onResume sẽ call 2 lần `refreshData`
        dẫn tới hiển thị nhiều snackBar không cần thiết.

        => chỉ show bên ios
        ___________
        new update 29/11/2022: bỏ show snackBar allcases: chỉ để lại khi click button refresh
      */
      // if (GetPlatform.isIOS) {
      //   Popup.instance.showSnackBar(
      //       message: LocaleKeys.calendar_refreshSuccess.tr,
      //       type: SnackbarType.success);
      // }
    }

    //
    _isOnInitialToDaySrcollOffSet = true;
  }

  void hideEventBusyOverlay() {
    busyAppointment.value = null;
  }

  void _handleSignInGoogle({dynamic mapJson, required Function onDone}) async {
    if (mapJson["access_token"] == null &&
        mapJson["authorization_code"] == null) {
      return;
    }
    try {
      await api.signInGoogle(data: mapJson, hasSync: true);
      calendarController.syncGoogle();
      Future.delayed(const Duration(seconds: 1)).then((_) {
        trackSyncGoogle();
      });
      onDone();
    } catch (e, s) {
      handleError(e, s);
    }
  }

  Future<void> signInGoogle({bool needToBack = true, Function? onDone}) async {
    void _handleGGResponse(dynamic result) {
      _handleSignInGoogle(
        mapJson: result,
        onDone: () async {
          _startSyncingTime = DateTime.now();
          syncMode.value = SyncingHeaderViewMode.syncingGoogle;

          showSyncHeaderView();

          onDone?.call();
          // lâu quá thì ẩn header
          Future.delayed(const Duration(seconds: 15)).then((_) async {
            if (syncMode.value == SyncingHeaderViewMode.syncingGoogle) {
              syncMode.value = SyncingHeaderViewMode.doneGoogle;
              closeSyncHeaderView();
            }
          });
        },
      );
    }

    final result = await Deeplink.signInGoogle();
    if (needToBack) {
      Utils.back();
    }

    if (result == null ||
        result["authorization_code"] == null ||
        result["authorization_code"] == "") {
      // user không grant full calendar scope
      Popup.instance.showAlert(
        title: LocaleKeys.calendar_sync_google_grant_scope_title.tr,
        message: LocaleKeys.calendar_sync_google_grant_scope_message.tr,
        doneBtn: AccentWorkPrimaryButton(
            LocaleKeys.calendar_sync_google_grant_scope_action.tr, () {
          Get.back();

          signInGoogle(needToBack: false, onDone: onDone);

          return;
        }),
      );
    } else {
      _handleGGResponse(result);
    }
  }

  Future<void> _handleSignOutGoogle(
      {required Function onDone, bool? isDeleteEvent}) async {
    try {
      Utils.back(closeOverlays: true);
      Deeplink.signOutGoogle();
      await api.signOutGoogle(calendarController.account?.email ?? "",
          isDeleteEvent: isDeleteEvent);
      TokenManager.saveGoogleEmail("");
      calendarController.signOutGoogle();
      onDone();
    } catch (e) {
      //hacking code: Xử lý khi người dùng đã huỷ đồng bộ rồi.
      if (e is BadRequestException) {
        trackSyncGoogle();
        onDone();
      } else {
        handleError(e, StackTrace.fromString(e.toString()));
      }
    }
  }

  Future<void> signOutGoogle({bool? isDeleteEvent}) async {
    _handleSignOutGoogle(
        onDone: () {
          Popup.instance.showSnackBar(
            message: LocaleKeys.calendar_cancel_sync_google_calendar_success.tr,
            type: SnackbarType.success,
          );
          createMeetingGoogleEmail = null;
          // need not reload data after sign out
          //getListItems();
        },
        isDeleteEvent: isDeleteEvent);
  }

  Future<void> trackSyncGoogle() async {
    if (FeatureFlag.enableFilterGoogle) {
      try {
        final result = await api.trackSyncGoogle();
        final bool hasSynced = result["has_synced"];
        final String email = result["email"] ?? "";
        final String avatar = result["avatar"] ?? "";
        final int expire = result["expire_at"] ?? 0;

        if (hasSynced) {
          calendarController.fillEmailInfo(email: email, avatarUrl: avatar);
          final ggScopeChecking = await api.googleScopeChecking();
          if (CheckScopeGoogleCalendar.hasFullCalendarScope(
              List<String>.from(ggScopeChecking["scopes"]))) {
          } else {
            syncMode.value = SyncingHeaderViewMode.syncGoogleExpired;
            showSyncHeaderView();
          }

          TokenManager.saveGoogleEmail(email);
          hideGGSyncNotification();
        } else {
          // nếu user chưa đăng nhập, `hasSynced` = false
          if (email.isNotEmpty) {
            createMeetingGoogleEmail = email;
            final expiredAt = DateTime.fromMillisecondsSinceEpoch(expire);
            if (expiredAt < DateTime.now()) {
              syncMode.value = SyncingHeaderViewMode.syncGoogleExpired;
              showSyncHeaderView();
            }
          } else {
            createMeetingGoogleEmail = null;
          }
          calendarController.signOutGoogle();
          showGGSyncNotification();
        }
      } catch (e) {
        logDebug("track sync google error $e");
      }
    }
  }

  Future<CalendarEventList> dragEventApi({
    required DateTime startTime,
    required CalendarEventList event,
    required bool sendNoti,
  }) async {
    final duration = DateTime.fromMillisecondsSinceEpoch(event.endAt!)
        .difference(DateTime.fromMillisecondsSinceEpoch(event.startAt!));
    var newEndTime = startTime.add(duration);

    // nếu mà new end time sang ngày mới thì lấy là 23:59 thôi
    if (newEndTime.weekday != startTime.weekday) {
      newEndTime =
          DateTime(startTime.year, startTime.month, startTime.day, 23, 59, 59);
    }

    // var scheduleValue = event.schedule?.value ?? [];
    // if (event.schedule?.type == CalendarEventScheduleType.daily ||
    //     event.schedule?.type == CalendarEventScheduleType.once) {
    //   scheduleValue = [startTime.millisecondsSinceEpoch];
    // }
    final targetIdentity = IdentityUtils.getIdentityFromDateTime(
        startTime, event.schedule?.offset);

    final newEvent = await api.dragEvent(
      id: event.id ?? '',
      startTime: startTime,
      endTime: newEndTime,
      isRecursive: event.isRecursiveEvent,
      identity: event.identity ?? '',
      targetIdentity: targetIdentity ?? '',
      sendNoti: sendNoti,
      // value: scheduleValue,
      // scheduleType: event.schedule?.type ?? CalendarEventScheduleType.daily,
      // type: event.type ?? CalendarEventType.meeting,
    );

    return newEvent;
  }

  Future<CalendarEventList> undoDraggedEventApi({
    required CalendarEventList event,
  }) async {
    try {
      final newEvent = await api.undoDraggedEvent(
        id: event.eventIdBeforeDragged ?? '',
      );
      return newEvent;
    } catch (e, trace) {
      logDebug(trace);
      logDebug(e);
      handleError(e, trace);
      rethrow;
    }
  }

  // MARK - Drag drop
  void onDragEnd(
      AppointmentDragEndDetails appointmentDragUpdateCallback) async {
    // lấy appointment vừa đc kéo
    late Appointment appointment;
    if (appointmentDragUpdateCallback.appointment is Appointment) {
      appointment = appointmentDragUpdateCallback.appointment as Appointment;
    } else {
      return;
    }

    // lấy instance của CalendarEventList trong appointment
    late CalendarEventList event;
    if (appointment.id is CalendarEventList) {
      event = appointment.id as CalendarEventList;
    } else if (appointment.recurrenceId is CalendarEventList) {
      event = appointment.recurrenceId as CalendarEventList;
    }

    // Check nếu mà kéo khoàng tgian nhỏ hơn 120s thì k làm gì cả
    if (event.startAt != null) {
      final previousAppointment = _appointmentDragStartDetails?.appointment;
      if (previousAppointment is Appointment) {
        final newAppointmentStartAt = appointment.startTime;
        final oldAppointmentStartAt = previousAppointment.startTime;
        final diff =
            oldAppointmentStartAt.difference(newAppointmentStartAt).inSeconds;
        if (diff.abs() < 120) {
          return;
        }
      }
    }

    // lấy droppingTime, thời gian vừa đc drag tới
    late DateTime droppingTime;
    if (appointmentDragUpdateCallback.droppingTime != null) {
      droppingTime = appointmentDragUpdateCallback.droppingTime!;
      // Nếu mà kéo thành sự kiện nhiều ngày thì modify nó thành sự kiện trong ngày
      // endtime là 23h59 của ngày hôm đó
      if (appointment.startTime.day != appointment.endTime.day) {
        final eventDuration = (event.endAt ?? 0) - (event.startAt ?? 0);
        final shiftedDroppingTimeInMiliseconds = DateTime(droppingTime.year,
                    droppingTime.month, droppingTime.day, 23, 59)
                .millisecondsSinceEpoch -
            eventDuration;
        droppingTime = DateTime.fromMillisecondsSinceEpoch(
            shiftedDroppingTimeInMiliseconds);
      }
    } else {
      return;
    }

    // hiện popup có send noti hay không
    var sendNotiResult = false;
    if (event.type == CalendarEventType.meeting) {
      final result = await Popup.instance.showBottomSheet(
        const DragDropConfirmNotifyDialog(),
      );
      sendNotiResult = result ?? false;
    }

    // Update identity của event đc drag dựa theo
    // vị trí ban đầu mà nó được drag
    // (origin identity của event, không phải root identity)
    final startDragAppointment = _appointmentDragStartDetails?.appointment;
    if (startDragAppointment is Appointment) {
      event.updateIdentity(startDragAppointment.startTime);
    }

    try {
      // gọi api drag event
      final newEvent = await dragEventApi(
        startTime: droppingTime,
        event: event,
        sendNoti: sendNotiResult == true,
      );

      // Gắn eventIdBeforeDragged cho cái event mới là id của event cũ
      newEvent.eventIdBeforeDragged = event.id;

      // tạo appointment mới từ new event và thêm vào data source
      final response = CalendarListEventResponse(data: [newEvent]);
      final newAppointment =
          response.mapToAppointments(isComparing: ownerIds.isNotEmpty).first;
      dataSource.appointments?.add(newAppointment);
      listItem.add(newAppointment);

      // xóa appointment cũ
      dataSource.appointments?.remove(appointment);
      listItem.remove(appointment);

      // reset data source
      dataSource
          .notifyListeners(CalendarDataSourceAction.add, [newAppointment]);
      dataSource
          .notifyListeners(CalendarDataSourceAction.remove, [appointment]);

      // tắt snackbar nếu đang có
      try {
        await Popup.instance.closeSnackBar(withAnimations: false);
      } catch (e, trace) {
        logDebug(e);
        logDebug(trace);
      }

      // hiện thông báo thành công
      final dateFormat = DateFormat('h:mm a', 'en');
      final dateTimeString = dateFormat.format(droppingTime);
      Popup.instance.showSnackBar(
        duration: 3.seconds,
        type: SnackbarType.success,
        message: LocaleKeys.calendar_drag_drop_update_success_message.tr
            .replaceFirst(
          '%',
          '$dateTimeString, ${Utils.dateTimeString(millisecondsSinceEpoch: droppingTime.millisecondsSinceEpoch, format: 'EEEE, d MMMM')}',
        ),
        undoTitle: LocaleKeys.calendar_drag_drop_undo_title.tr,
        undoCallback: () async {
          _rollBackDraggedEvent(
            event: newEvent,
            appointment: newAppointment,
          );
          // undo
          try {
            // await Popup.instance.closeSnackBar(withAnimations: false); sẽ bị lỗi
            await Popup.instance.closeSnackBar();
          } catch (e, trace) {
            logDebug(e);
            logDebug(trace);
          }
        },
      );
    } catch (e, trace) {
      logDebug(trace);
      logDebug(e);
      handleError(e, trace);
    }
  }

  AppointmentDragStartDetails? _appointmentDragStartDetails;
  void onDragStart(AppointmentDragStartDetails appointmentDragStartDetails) {
    _appointmentDragStartDetails = appointmentDragStartDetails;
  }

  void _rollBackDraggedEvent({
    required CalendarEventList event,
    required Appointment appointment,
    // required Appointment startAppointment,
  }) async {
    // check có id của event cũ
    if (event.eventIdBeforeDragged == null) return;

    // call api
    await undoDraggedEventApi(event: event);

    await delayGetListItems();
  }

  void checkMSSync() async {
    // Reset outlook account và ẩn header, tránh trường hợp cache lại từ trước,
    // ví dụ case logout
    outlookAccounts.value = [];
    closeSyncHeaderView();

    // Get outlook accounts từ BE
    // nếu có account thì set sẵn status = hết hạn, status này sẽ check và update lại ở dưới
    try {
      logWithHighlight('START GETTING OUTLOOK ACCOUNTS FROM BE');
      final result = await api.getSyncAuth();
      logWithHighlight('COMPLETE GETTING OUTLOOK ACCOUNT FROM BE');
      logWithHighlight('FOUND ${result.length} ACCOUNT(S)');
      if (result.isNotEmpty) {
        // result.firstOrNull?.signinRequired = true;
        outlookAccounts.value = result;
        outlookAccounts.refresh();
      }
    } catch (e, trace) {
      logDebug(e);
      logDebug(trace);
      handleError(e, trace);
      return;
    }

    // Nếu có account thì check xem có refresh token đc k
    if (outlookAccounts.isNotEmpty) {
      // try {
      //   final outlookAccount = outlookAccounts.first;
      //   logWithHighlight('START GETTING LOCAL OUTLOOK USER');
      //   final localUser = await getOutlookLocalUser();
      //   logWithHighlight('COMPLETE GETTING LOCAL OUTLOOK USER');
      //   // Nếu có local user thì check token hết hạn chưa
      //   // còn nếu k có local user thì phải yêu cầu login lại
      //   if (localUser != null) {
      //     // final currentOutlookAccountFromSDK =
      //     //     Deeplink.msalLoadCurrentAccount();

      //     logWithHighlight('OUTLOOK LOCAL USER != NULL');
      //     logWithHighlight('INIT isTokenExpired = false');
      //     var isTokenExpired = false;
      //     // refresh token
      //     logWithHighlight('START ACQUIRE TOKEN SILENTLY');
      //     final tokenResult =
      //         await CalendarDeeplink.msalAcquireCurrentAccountTokenSilently();
      //     logWithHighlight('COMPLETE ACQUIRE TOKEN SILENTLY');

      //     if ((tokenResult?.isNotEmpty == true) &&
      //         tokenResult != localUser.accessToken) {
      //       isTokenExpired = true;
      //       // update lại token và user để lưu lại
      //       localUser.accessToken = tokenResult!;
      //       updateOutlookLocalUser(localUser);
      //     }

      //     // Nếu token hết hạn và refresh ngầm thành công thì ko yêu cầu signin
      //     // và update lại access token lên BE
      //     if (isTokenExpired) {
      //       if (tokenResult is String) {
      //         // Update lại access token lên BE
      //         await updateOutlookRemoteData(localUser);

      //         // Chạy timer
      //         startAutoRefreshingOutlookToken();

      //         // Update UI không yêu cầu signin
      //         outlookAccount.signinRequired = false;
      //       }
      //     } else {
      //       // Chạy timer
      //       startAutoRefreshingOutlookToken();

      //       // Update UI không yêu cầu signin
      //       outlookAccount.signinRequired = false;
      //     }
      //   } else {
      //     logWithHighlight('OUTLOOK LOCAL USER == NULL');
      //     // have no local user -> set token is expired
      //     // Mark to display to UI
      //     outlookAccount.signinRequired = true;

      //     // Hiện status là hết hạn
      //     syncMode.value = SyncingHeaderViewMode.syncOutlookExpired;
      //     showSyncHeaderView();
      //   }

      //   // Update UI
      //   outlookAccounts.value = [outlookAccount];
      // } catch (e, trace) {
      //   // HANDLE case msalAcquireCurrentAccountTokenSilently failed
      //   if (e is MsalNoAccountException) {
      //     logWithHighlight('GET MsalNoAccountException');
      //     logWithHighlight(e.errorMessage);
      //     // Hiện status là hết hạn
      //     syncMode.value = SyncingHeaderViewMode.syncOutlookExpired;
      //     showSyncHeaderView();
      //   } else {
      //     // show list UI with signin required
      //     logDebug(e);
      //     logDebug(trace);
      //     // handleError(e);
      //   }
      // }

      syncMode.value = SyncingHeaderViewMode.doneOutlook;
    } else {
      syncMode.value = SyncingHeaderViewMode.syncOutlookExpired;
    }
  }

  void onSyncMSToggle({
    bool fromBottomSheet = true,
  }) async {
    // final bool needAuthorize = box.read(kMsalNeedAuthorize) ?? false;

    Future _onSyncSuccess(
        [String accessTokenResult = "", String refreshToken = ""]) async {
      // Display Syncing status
      syncMode.value = SyncingHeaderViewMode.syncingOutlook;
      showSyncHeaderView();
      _startSyncingTime = DateTime.now();

      // Call graph api to get avatar
      final accessToken = accessTokenResult;
      final userProfileResponse = await getUserProfile(accessToken);
      final userId = userProfileResponse['id'];
      final email = userProfileResponse['userPrincipalName'];
      final username = email;

      String? avatarUrl;
      try {
        final photoResponse = await getUserPhoto(accessToken);
        final uploadAPI = UploadAPI();
        final uploadResponse =
            await uploadAPI.uploadImageFromBytes(bytes: photoResponse!);
        avatarUrl = uploadResponse.src;
      } catch (e, trace) {
        GPCoreTracker().appendError(
          'Flutter.calendar.onSyncMSToggle.error',
          data: {'error': e, 'stacktrace': trace},
        );
        GPCoreTracker().sendLog(
          message: 'Flutter.calendar.onSyncMSToggle.error',
          trackerType: GPTrackerType.calendar,
        );

        logDebug(e);
        logDebug(trace);
      }

      // Save local user and token expired time
      final localUser = OutlookLocalDataModel(
        expiredTime: DateTime.now(),
        accessToken: accessToken,
        avatar: avatarUrl ?? '',
        email: username ?? '',
      );
      updateOutlookLocalUser(localUser);

      // Call sync api
      await api.postSyncAuth(
        accessToken: accessToken,
        avatar: avatarUrl,
        email: email,
        expireAt: 0,
        userId: userId,
        refreshToken: refreshToken,
        sendNotifcation: true,
      );

      // Update user UI
      final user = SyncOutlookAccountModel(
        id: username ?? '',
        code: '',
        email: email ?? '',
        avatar: avatarUrl ?? '',
        accessToken: accessToken,
      );

      outlookAccounts.value = [user];

      box.write(kMsalNeedAuthorize, true);
      // Refresh list
      // await getListItems();

      // // Display sync successfully status
      // syncMode.value = SyncingHeaderViewMode.doneOutlook;

      // Future.delayed(5.seconds).then((_) => closeSyncHeaderView());
    }

    try {
      final result =
          // !needAuthorize
          //     ? await CalendarDeeplink.msalAcquireTokenInteractively()
          //     :
          await authorize(Get.context!);

      if (result is String) {
        if (fromBottomSheet) {
          // Dismiss popup
          Utils.back(closeOverlays: true);
        }

        // if (!needAuthorize) {
        //   await _onSyncSuccess(result);
        // } else {
        final Map oauth2TokenResponse = await oauth2Token(result);
        await _onSyncSuccess(
          oauth2TokenResponse["access_token"],
          oauth2TokenResponse["refresh_token"],
        );
        // }
      }
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.calendar.authorize.error',
        data: {'error': e, 'stacktrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar.authorize.error',
        trackerType: GPTrackerType.calendar,
      );

      logDebug(e);
      logDebug(trace);

      // Trường hợp user bấm cancel khi login thì sẽ trả về lỗi này,
      // Bản chất k phải lỗi nên k cần handle error
      if (e is MsalInvalidConfigurationException) {
        /*
         `CalendarDeeplink.msalAcquireTokenInteractively()` error vì khác scope,
         _initialize sẽ throw exception

         scope default không thể bao gồm: "openid", "profile", "offline_access":
        */
        box.write(kMsalNeedAuthorize, true);

        final bool isAuthorized = box.read(kMsalAuthorized) ?? false;

        if (!isAuthorized) {
          box.write(kMsalAuthorized, true);
          onSyncMSToggle(fromBottomSheet: fromBottomSheet);
          return;
        }
      } else if (e is MsalUserCancelledException) {
        return;
      } else if (e is MsalException) {
        if (e.errorMessage == 'Authentication error') {
          closeSyncHeaderView();
          return;
        }
      }

      closeSyncHeaderView();
      handleError(e, trace);
    }
  }

  void showSyncHeaderView() {
    syncingViewHeight.value = 48;
  }

  void closeSyncHeaderView() {
    syncingViewHeight.value = 0;
  }

  void _unsyncOutlook(String email) async {
    try {
      await api.deleteSyncAuth(email: email);

      // Data
      removeOutlookLocalUser();
      stopAutoRefreshingOutlookToken();

      box.write(kMsalNeedAuthorize, false);

      // UI
      outlookAccounts.value = [];

      // Snackbar
      Popup.instance.showSnackBar(
          message: LocaleKeys.calendar_sync_outlook_title_snackbar_unsync.tr,
          type: SnackbarType.success);
    } catch (e, trace) {
      logDebug(e);
      logDebug(trace);
      handleError(e, trace);
    }
  }

  void onSyncingHeaderViewTapped({
    bool fromBottomSheet = false,
  }) async {
    if (syncMode.value == SyncingHeaderViewMode.syncingGoogle ||
        syncMode.value == SyncingHeaderViewMode.syncingOutlook) {
      return;
    }

    final bool isSyncGoogle =
        syncMode.value == SyncingHeaderViewMode.syncGoogleExpired;

    Future _syncGoogle() async {
      final result =
          await Popup.instance.showBottomSheet(const SyncGoogleDialog());

      // result = true khi user chọn ngắt đồng bộ
      // result = false khi user chọn đồng bộ lại
      if (result == true) {
        if (fromBottomSheet) {
          Get.back();
        }

        await signOutGoogle();

        closeSyncHeaderView();
      } else if (result == false) {
        await authGoogle();
      }
    }

    Future _syncOutlook() async {
      final result = await Popup.instance
          .showBottomSheet(const UnsyncOrSyncOutlookDialog());

      // result = true khi user chọn ngắt đồng bộ
      // result = false khi user chọn đồng bộ lại
      if (result == true) {
        final email = outlookAccounts.first.email;
        _unsyncOutlook(email);

        if (fromBottomSheet) {
          Get.back();
        }

        closeSyncHeaderView();
      } else if (result == false) {
        box.write(kMsalAuthorized, false);
        onSyncMSToggle(
          fromBottomSheet: fromBottomSheet,
        );
      }
    }

    if (isSyncGoogle) {
      await _syncGoogle();
    } else {
      await _syncOutlook();
    }
  }

  void onRemoveOutlookAccountPressed(
    SyncOutlookAccountModel acc, {
    bool fromBottomSheet = true,
  }) async {
    // Trường hợp đã hết hạn -> icon lúc này là nút resync -> hiện popup re-sync
    if (acc.signinRequired) {
      onSyncingHeaderViewTapped(fromBottomSheet: fromBottomSheet);
    } else {
      // Trường hợp chưa hết hạn -> icon lúc này là nút unsync -> hiện popup unsync
      Get.back();
      final result =
          await Popup.instance.showBottomSheet(const UnsyncOutlookDialog());
      if (result == true) {
        _unsyncOutlook(acc.email);
      }
    }
  }

  void onSelectFilters(List<FilterOutlookChipItem> p1) {
    selectedFilters = p1;
    getListItems();
  }

  void resetFilters() {
    selectedFilters = List.from(kDefaultFilterEventAndSource);
  }

  void onScrollChanged(double scrollOffset) {
    if (_isOnInitialToDaySrcollOffSet) _isOnInitialToDaySrcollOffSet = false;
  }

  void onClickToday() {
    calendarController.displayDate = DateTime.now();
    _isOnInitialToDaySrcollOffSet = true;
  }

  Future<void> getNotifySyncSuccessfully() async {
    //
    await getListItems();

    // Display sync successfully status
    syncMode.value = SyncingHeaderViewMode.doneOutlook;
    switch (syncMode.value) {
      case SyncingHeaderViewMode.syncingGoogle:
        syncMode.value = SyncingHeaderViewMode.doneGoogle;
        break;
      case SyncingHeaderViewMode.syncingOutlook:
        syncMode.value = SyncingHeaderViewMode.doneOutlook;
        break;
      default:
        break;
    }

    Future.delayed(2.seconds).then((_) => closeSyncHeaderView());
  }

  @override
  Future<void> onLoadmore(DateTime startDate, DateTime endDate) async {
    request.eventFromAt = startDate.millisecondsSinceEpoch;
    request.eventFromTo = endDate.millisecondsSinceEpoch;
    await getListItems();
  }

  /// Delay trước khi load lại list event
  Future<void> delayGetListItems({CalendarEventList? event}) async {
    await Future.delayed(const Duration(milliseconds: 900));
    await getListItems(event: event);
  }

  Future getConfigs() async {
    try {
      final response = await api.getConfigs();
      if (response != null) {
        if (response["data"] != null &&
            response["data"]["is_disable_acceptance"] != null) {
          FeatureFlag.enableCalendarAcceptDenyEvent =
              !response["data"]["is_disable_acceptance"];
        }
      }
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.calendar.getConfigs.error',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar.getConfigs.error',
        trackerType: GPTrackerType.calendar,
      );
    }
  }

  void showGGSyncNotification() {
    headerHeight = 128;
    showGGSyncNoti.value = true;
  }

  void hideGGSyncNotification() {
    headerHeight = 96;
    showGGSyncNoti.value = false;
  }
}

enum CalendarTabs { calendar, meetingRooms }

extension TabTitle on CalendarTabs {
  String get title {
    switch (this) {
      case CalendarTabs.calendar:
        return LocaleKeys.calendar_room_tab_calendar_tab.tr;
      case CalendarTabs.meetingRooms:
        return LocaleKeys.calendar_room_tab_booking_room_tab.tr;
    }
  }
}
