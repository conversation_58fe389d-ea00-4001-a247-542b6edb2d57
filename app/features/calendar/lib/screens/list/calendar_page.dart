import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_calendar/gp_calendar.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_event_type.dart';
import 'package:gp_feat_calendar/screens/detail/models/calendar_event_detail_input.dart';
import 'package:gp_feat_calendar/screens/list/mixins/calendar_config.dart';
import 'package:gp_feat_calendar/utils/deeplink.dart';
import 'package:gp_feat_calendar/widgets/gp_fab_menu.dart';
import 'package:gp_feat_calendar/widgets/pattern_background.dart';
import 'package:sliding_sheet/sliding_sheet.dart';

import 'booking_room/calendar_booking_room_controller.dart';
import 'booking_room/calendar_booking_room_view.dart';
import 'change_view_type.dart';
import 'components/bottom_comparing_calendars_view.dart';
import 'components/busy/event_busy_widget.dart';
import 'components/syncing_header_view.dart';
import 'controller.dart';
import 'models/event_list_response.dart';

class CalendarBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(CalendarListController());
    Get.lazyPut(() => CalendarBookingRoomController());
  }
}

class CalendarPage extends GetView<CalendarListController> {
  const CalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _GPCalendarView();
  }
}

class _GPCalendarView extends GetView<CalendarListController>
    with CalendarConfig {
  const _GPCalendarView();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    return SafeArea(
      child: Scaffold(
        floatingActionButton: !kDebugTool
            ? null
            : FloatingActionButton(
                mini: true,
                onPressed: () {
                  Get.toNamed(RouterName.log);
                },
                backgroundColor: GPColor.workPrimary,
                child: const Icon(Icons.search, color: Colors.white, size: 18),
              ),
        floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
        body: Obx(
          () => GPFabMenu(
            icon: AnimatedIcons.menu_close,
            fabColor: GPColor.functionAccentWorkSecondary,
            fabOpenColor: GPColor.bgSecondary,
            blur: 0,
            iconColor: GPColor.contentInversePrimary,
            iconOpenColor: GPColor.contentPrimary,
            onMenuTap: (type) {
              controller.currentFocusDateTime = _getDateTimeBeforeClickAddBtn();
              _onFabMenuTap(type);
            },
            onButtonTap: () {
              if (controller.isBookingRoom.value) {
                final bookingController =
                    Get.find<CalendarBookingRoomController>();
                bookingController.onFabButtonTap();
                return false;
              }
              return true;
            },
            showButton: controller.showFabMenu.value,
            body: Column(
              children: [
                const SyncingHeaderView(),
                Expanded(
                  child: Obx(
                    () => Stack(
                      children: [
                        if (controller.isBookingRoom.value)
                          const CalendarBookingRoom()
                        else
                          GPCalendar(
                            controller: controller.calendarController,
                            dataSource: controller.dataSource,
                            allowedViews: controller.allowedViews,
                            onViewChanged: _onViewChanged,
                            onDragStart: controller.onDragStart,
                            onDragEnd: controller.onDragEnd,
                            allowDragAndDrop: true,
                            dragAndDropSettings: const DragAndDropSettings(
                              autoNavigateDelay: Duration(minutes: 15),
                              indicatorTimeFormat: 'h:mm a',
                              showTimeIndicator: true,
                            ),
                            onTap: _onTapDetail,
                            onTapCreateAlldayEvent: (details) =>
                                _onTapDetail(details, isAllDay: true),
                            view: controller.currentView,
                            onLongPress: _onLongPress,
                            onClickRefreshCalendar: _onClickRefreshData,
                            onScrollChanged: (scrollOffset) {
                              controller.calendarScrollOffset = scrollOffset;
                              controller.onScrollChanged(scrollOffset);
                            },
                            // loadMoreWidgetBuilder: (BuildContext context,
                            //     LoadMoreCallback loadMoreAppointments) {
                            //   return FutureBuilder<void>(
                            //     future:
                            //         controller.currentView == CalendarView.schedule
                            //             ? loadMoreAppointments()
                            //             : null,
                            //     builder: (context, snapShot) {
                            //       return const SizedBox();
                            //     },
                            //   );
                            // },
                            // default configs
                            // showNavigationArrow: model.isWebFullView,
                            backgroundColor: GPColor.bgPrimary,
                            headerHeight: controller.headerHeight,
                            showDatePickerButton: true,
                            showWeekNumber: false,
                            appointmentTextStyle: CalendarConfig.appoimentStyle,
                            headerStyle: CalendarConfig.headerStyle,
                            headerDateFormat: CalendarConfig.headerFormat,
                            todayTextStyle: CalendarConfig.todayTextStyle,
                            todayHighlightColor:
                                GPColor.functionAccentWorkSecondary,
                            monthViewSettings: CalendarConfig.monthViewSettings,
                            timeSlotViewSettings:
                                CalendarConfig.timeSlotViewSettings,
                            viewHeaderStyle: CalendarConfig.viewHeaderStyle,
                            weekNumberStyle: CalendarConfig.weekNumberStyle,
                            cellEndPadding: 4,
                            cellBorderColor: GPColor.lineTertiary,
                            firstDayOfWeek: 1,
                            isHasData: controller.isHasData.value,
                            onToggleViewType: _changeCalendarView,
                          ),
                        Positioned(
                          height: 48,
                          top: 48,
                          width: MediaQuery.of(context).size.width,
                          child: Container(
                              color: GPColor.bgPrimary,
                              child: GPTabBar(
                                tabController: controller.tabController,
                                tabs: controller.tabs
                                    .map((e) => e.title)
                                    .toList(),
                              )),
                        ),
                        controller.busyAppointment.value != null
                            ? EventBusyWidget(
                                appointmentBusyModel:
                                    controller.busyAppointment.value!,
                              )
                            : const SizedBox(),
                        if (controller.showGGSyncNoti.value)
                          Positioned(
                            top: 96,
                            width: MediaQuery.of(context).size.width,
                            height: 32,
                            child: MessagePatternBackground(
                              backgroundColor: GPColor.orangeLight,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgWidget(
                                      'assets/images/svg/ic16-fill-informationmark-circle.svg',
                                      color: GPColor.orangeDarker,
                                      width: 16,
                                      height: 16,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      "Chưa đồng bộ với lịch Google.",
                                      style:
                                          textStyle(GPTypography.headingSmall)
                                              ?.copyWith(
                                                  color: GPColor.orangeDarker,
                                                  height: 1,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600),
                                    ),
                                    const SizedBox(width: 4),
                                    InkWell(
                                      child: Text(
                                        "Đồng bộ ngay",
                                        style: textStyle(
                                                GPTypography.headingSmall)
                                            ?.copyWith(
                                                color: GPColor.orangeDarker,
                                                height: 1,
                                                decoration:
                                                    TextDecoration.underline,
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600),
                                      ),
                                      onTap: () {
                                        controller.signInGoogle(
                                            needToBack: false);
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Obx(
          () => controller.listColleagues.isNotEmpty
              ? BottomComparingCalendarsView(
                  onClose: controller.cancelCompare,
                  onCompare: _onCompareColleagues,
                  message: controller.messageComparing,
                )
              : const SizedBox(),
        ),
      ),
    );
  }

  Future<void> _onToggleSyncGoogleCalendar(bool needSignIn) async {
    if (needSignIn) {
      controller.signInGoogle();
    } else {
      await Popup.instance.showBottomSheet(
        ConfirmDeleteDialog(
          title: LocaleKeys.calendar_unsync_google_calendar.tr,
          content: LocaleKeys.calendar_confirm_to_unsync_google_calendar.tr,
          cancelButtonTitle:
              LocaleKeys.calendar_create_button_back_discard_event.tr,
          confirmButtonTitle: LocaleKeys.calendar_usync_button_title.tr,
          icon: SvgWidget(
            'assets/images/svg/ic24-line15-2arrow-rotate-circle-slash.svg',
            width: 64,
            height: 64,
            color: GPColor.functionNegativePrimary,
          ),
          onConfirmDeleteHasCheckbox: (isDeleteEvent) async {
            controller.signOutGoogle(isDeleteEvent: isDeleteEvent);
          },
          checkBoxTitle: LocaleKeys.calendar_sync_remove_google_event.tr,
          initCheckBoxState: true,
        ),
      );
    }
  }

  void _onCompareColleagues() async {
    /*
      update 31/03: ios and android use Platform navigator now
    */
    // if (GetPlatform.isIOS) {
    try {
      final result = await CalendarDeeplink.openFindingColleagues(
        assignees: controller.listColleagues,
      );
      controller.selectColleagues(result);
      Get.back();
      return;
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.calendar._onCompareColleagues.error',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar._onCompareColleagues.error',
        trackerType: GPTrackerType.calendar,
      );

      logDebug(e);
    }
    // }
    // // android
    // // also run directly from flutter
    // _navigateToFindingColleagues();
  }

  // void _navigateToFindingColleagues() async {
  //   final result = await Get.toNamed(
  //     RouterName.selectInvitees,
  //     arguments: SelectInviteesOptions(
  //       tabs: [
  //         SelectInviteeTabs.member,
  //       ],
  //       title: LocaleKeys.calendar_calendar_comparison.tr,
  //       actionButtonTitle: LocaleKeys.calendar_done.tr,
  //       selectedMembers: controller.listColleagues,
  //     ),
  //   );
  //   if (result is SelectInviteesResult) {
  //     controller.selectColleagues(result.selectedMembers ?? []);
  //     Get.back();
  //   } else {
  //     //do nothing
  //   }
  // }

  Future _onClickRefreshData() async {
    await controller.getListItems();
    Popup.instance.showSnackBar(
        message: LocaleKeys.calendar_refreshSuccess.tr,
        type: SnackbarType.success);
  }

  Future<void> _onTapDetail(
    CalendarTapDetails details, {
    bool isAllDay = false,
  }) async {
    if (controller.currentView != CalendarView.month) {
      _removeCalendarSelectedDateFocus();
    }

    controller.busyAppointment.value = null;

    if (details.date != null) {
      controller.currentFocusDateTime = details.date!;
      if (details.appointments == null) {
        _onFabMenuTap(
          CalendarEventType.meeting,
          duration: const Duration(hours: 1),
          isAllDay: isAllDay,
        );
      } else {
        if (details.appointments == null || details.appointments!.isEmpty) {
          // _onFabMenuTap(CalendarEventType.meeting);
          controller.currentView = CalendarView.day;
          controller.calendarController.displayDate = details.date;
          controller.calendarController.view = controller.currentView;
        } else {
          if ((details.appointments?.length ?? 1) > 1) {
            controller.currentView = CalendarView.day;
            controller.calendarController.displayDate = details.date;
            controller.calendarController.view = controller.currentView;
            await controller.getListItems();
            // remove focus khi chuyển về từ view month -> day
            _removeCalendarSelectedDateFocus();
            return;
          } else {
            CalendarEventList? _oldCalendarEventList;
            if (details.appointments?.first.id is CalendarEventList) {
              _oldCalendarEventList = details.appointments?.first.id;
            } else if (details.appointments?.first.recurrenceId
                is CalendarEventList) {
              _oldCalendarEventList = details.appointments?.first.recurrenceId;
            }
            if (_oldCalendarEventList != null) {
              CalendarEventList _calendarEventList =
                  _oldCalendarEventList.clone();

              _calendarEventList.updateIsRootEvent(details.appointments?.first);
              _calendarEventList.updateTime(details.appointments?.first);
              _calendarEventList
                  .updateIdentity(details.appointments?.first.startTime);

              if (_calendarEventList.isOwner) {
                final calendarEventDetailInput = CalendarEventDetailInput(
                    scenario: CalendarDetailScenario.calendar,
                    event: _calendarEventList,
                    googleEmail: controller.googleEmail);
                try {
                  final result = await CalendarDeeplink.openCalendarDetail(
                      calendarEventDetailInput);
                  if (result != null) {
                    final event = CalendarEventList.fromJson(
                        Map<String, dynamic>.from(result));
                    controller.delayGetListItems(event: event);
                  }
                } catch (e) {
                  await _goToDetail(_calendarEventList);
                }
              } else {
                controller.busyAppointment.value = details;
              }
            }
          }
        }
      }
    }
  }

  Future _goToDetail(CalendarEventList _calendarEventList) async {
    // push to detail
    // var _ret =
    await Get.toNamed(
      CalendarRouterName.calendarEventDetail,
      arguments: _calendarEventList.isOwner
          ? [_calendarEventList]
          : _calendarEventList.id,
    );
    // if (_ret == CalendarListAction.refresh) {
    //   await controller.getListItems();
    // }
  }

  Future _goToCreate(
    CalendarEventType calendarEventType, {
    Duration duration = const Duration(minutes: 30),
    bool isAllDay = false,
  }) async {
    return await Get.toNamed(CalendarRouterName.createCalendarEvent,
        arguments: [
          controller.currentFocusDateTime,
          calendarEventType,
          duration,
          isAllDay,
          null,
          controller.googleEmail,
          null,
          controller.isSynced,
        ])?.then((value) {
      if (value is CalendarEventList) {
        controller.delayGetListItems(event: value);
      } else {
        controller.getListItems();
      }
    });
  }

  // TODO move to detail
  Future<void> _onLongPress(
      CalendarLongPressDetails calendarLongPressDetails) async {
    _removeCalendarSelectedDateFocus();
  }

  DateTime _getDateTimeBeforeClickAddBtn() {
    if (controller.currentView == CalendarView.day) {
      return _nowPlus30Mins();
    }
    if (controller.currentView == CalendarView.schedule) {
      return _nowPlus30Mins();
      // try {
      //   const topOffset = Offset(100, 0);
      //   final displayTopDetail =
      //       controller.calendarController.getScheduleViewDetails(topOffset);
      //   if (displayTopDetail != null) {
      //     if (displayTopDetail.date != null) {
      //       final now = DateTime.now();
      //       final result = DateTime(
      //         displayTopDetail.date!.year,
      //         displayTopDetail.date!.month,
      //         displayTopDetail.date!.day,
      //         now.hour,
      //         now.minute,
      //       ).add(const Duration(minutes: 30));

      //       return result;
      //     }
      //   }
      // } catch (e) {
      //   log("getScheduleViewDetails ${e.toString()}");
      //   //do nothing
      // }
    }

    final _currentDateTime = DateTime.now();
    return DateTime(
        _currentDateTime.year,
        _currentDateTime.month,
        _currentDateTime.day,
        _currentDateTime.hour,
        _currentDateTime.minute + 30);
  }

  DateTime _nowPlus30Mins() {
    return DateTime.now().add(30.minutes);
  }

  void _onFabMenuTap(
    CalendarEventType calendarEventType, {
    Duration duration = const Duration(minutes: 30),
    bool isAllDay = false,
  }) async {
    try {
      final result = await CalendarDeeplink.openCreateCalendarEvent(
          dateTime: controller.currentFocusDateTime,
          type: calendarEventType,
          duration: duration,
          isAllDay: isAllDay,
          googleEmail: controller.googleEmail,
          isSynced: controller.isSynced);
      if (result is Map) {
        final createdEvent =
            CalendarEventList.fromJson(Map<String, dynamic>.from(result));
        controller.delayGetListItems(event: createdEvent);
      }
    } catch (e) {
      logDebug(e);
      await _goToCreate(calendarEventType,
          duration: duration, isAllDay: isAllDay);
    }
  }

  void _onViewChanged(ViewChangedDetails viewChangedDetails) {
    controller.currentView = controller.calendarController.view!;
    controller.currentFocusDateTime = viewChangedDetails.visibleDates.first;

    controller.updateRequestDateTimeIfNeeded(viewChangedDetails.visibleDates);
  }

  void _removeCalendarSelectedDateFocus() {
    controller.calendarController.selectedDate = null;
  }

  void _changeCalendarView() {
    // Khi đang đồng bộ với GG thì chặn hành vi này
    // if (widget.controller.isSyncingGoogle) {
    //   return;
    // }
    double heightScreen = MediaQuery.of(Get.context!).size.height;
    var snapFirst = 615 / heightScreen;
    if (snapFirst >= 1) {
      snapFirst = 0.8;
    }
    showSlidingBottomSheet(
      Get.context!,
      builder: (context) => SlidingSheetDialog(
        avoidStatusBar: true,
        cornerRadiusOnFullscreen: 0,
        cornerRadius: 16,
        controller: controller.sheetController,
        duration: const Duration(milliseconds: 400),
        // padding: const EdgeInsets.only(top: 10),
        // margin: const EdgeInsets.all(0),
        listener: (state) {
          controller.maxExtentListenable.value = state.isExpanded;
        },
        builder: (_, __) {
          return Material(
            color: Colors.white,
            child: ChangeCalendarViewBottomSheet(
              syncGoogleHandler: controller.calendarController,
              localizations: GPCalendarLocalizations.of(context),
              calendarView: controller.calendarController.view!,
              onChangeCalendarView: (p0) =>
                  controller.calendarController.view = p0,
              sheetController: controller.sheetController,
              eventTypes: controller.gPCalendarEventTypes,
              onUpdateCalendar: controller.getListItems,
              onClickToday: controller.onClickToday,
              onUpdateEventType: controller.onUpdateEventTypes,
              onCompareColleagues: _onCompareColleagues,
              onToggleSyncGoogleCalendar: _onToggleSyncGoogleCalendar,
              onSyncOutlookTapped: controller.onSyncMSToggle,
              outlookAcounts: controller.outlookAccounts,
              onRemovePressed: controller.onRemoveOutlookAccountPressed,
              selectedFilters: controller.selectedFilters,
              onSelectFilter: controller.onSelectFilters,
              canUpdateFilters: !controller.inComparingColleagueMode,
            ),
          );
        },
        headerBuilder: (_, state) {
          return Material(
            color: Colors.white,
            child: SizedBox(
              height: 48,
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                ValueListenableBuilder(
                    valueListenable: controller.maxExtentListenable,
                    builder: (context, bool value, child) {
                      return !value
                          ? const SizedBox(height: 48, width: 48)
                          : BackButton(color: GPColor.contentPrimary);
                    }),
                Expanded(
                  child: Center(
                    child: Text(
                      GPCalendarLocalizations.of(context)
                          .bottomSheetChangeCalendarView
                          .title,
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: GPColor.contentPrimary),
                    ),
                  ),
                ),
                const SizedBox(width: 48)
              ]),
            ),
          );
        },
        snapSpec: SnapSpec(
          initialSnap: snapFirst,
          snap: true,
          snappings: [snapFirst, 1.0],
        ),
      ),
    );
  }
}
